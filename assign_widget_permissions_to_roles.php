<?php

require_once 'vendor/autoload.php';

// تحميل Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 إضافة صلاحيات الويدجت للأدوار الموجودة\n";
echo "==========================================\n\n";

use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

// قائمة صلاحيات الويدجت
$widgetPermissions = [
    'widget_MainStatsOverview',
    'widget_ContentStatsWidget',
    'widget_EngagementStatsWidget',
    'widget_MetalPricesWidget',
    'widget_RecentOrdersWidget',
    'widget_UpcomingAppointmentsWidget',
    'widget_SalesChartWidget',
    'widget_SystemStatusWidget',
];

// تعريف الأدوار وصلاحيات الويدجت المناسبة لكل دور
$roleWidgetPermissions = [
    'super_admin' => $widgetPermissions, // جميع الويدجت
    'admin' => [
        'widget_MainStatsOverview',
        'widget_ContentStatsWidget',
        'widget_EngagementStatsWidget',
        'widget_MetalPricesWidget',
        'widget_RecentOrdersWidget',
        'widget_UpcomingAppointmentsWidget',
        'widget_SalesChartWidget',
        // لا يشمل widget_SystemStatusWidget
    ],
    'manager' => [
        'widget_MainStatsOverview',
        'widget_ContentStatsWidget',
        'widget_EngagementStatsWidget',
        'widget_MetalPricesWidget',
        'widget_RecentOrdersWidget',
        'widget_UpcomingAppointmentsWidget',
        'widget_SalesChartWidget',
        // لا يشمل widget_SystemStatusWidget
    ],
];

// إضافة الصلاحيات للأدوار
foreach ($roleWidgetPermissions as $roleName => $permissions) {
    $role = Role::where('name', $roleName)->first();
    
    if (!$role) {
        echo "⚠️ الدور غير موجود: {$roleName}\n";
        continue;
    }

    echo "🎭 معالجة دور: {$roleName}\n";
    
    $addedCount = 0;
    $existingCount = 0;
    
    foreach ($permissions as $permissionName) {
        $permission = Permission::where('name', $permissionName)->first();
        
        if (!$permission) {
            echo "   ⚠️ الصلاحية غير موجودة: {$permissionName}\n";
            continue;
        }
        
        if (!$role->hasPermissionTo($permissionName)) {
            $role->givePermissionTo($permissionName);
            echo "   ✅ تم إضافة: {$permissionName}\n";
            $addedCount++;
        } else {
            echo "   ℹ️ موجودة مسبقاً: {$permissionName}\n";
            $existingCount++;
        }
    }
    
    echo "   📊 ملخص: {$addedCount} جديدة، {$existingCount} موجودة\n\n";
}

// عرض ملخص الصلاحيات لكل دور
echo "📋 ملخص صلاحيات الويدجت لكل دور:\n";
echo "=====================================\n\n";

foreach ($roleWidgetPermissions as $roleName => $permissions) {
    $role = Role::where('name', $roleName)->first();
    if (!$role) continue;

    echo "🎭 دور: {$roleName}\n";
    echo "   📈 الويدجت المتاحة (" . count($permissions) . "/" . count($widgetPermissions) . "):\n";
    
    foreach ($widgetPermissions as $widget) {
        $hasAccess = in_array($widget, $permissions);
        $status = $hasAccess ? '✅' : '❌';
        $widgetName = str_replace('widget_', '', $widget);
        echo "      {$status} {$widgetName}\n";
    }
    
    echo "\n";
}

echo "🎉 تم الانتهاء من إضافة صلاحيات الويدجت!\n\n";

echo "💡 الآن يمكنك:\n";
echo "1. زيارة صفحة الأدوار في لوحة التحكم\n";
echo "2. تعديل أي دور وإضافة/إزالة صلاحيات الويدجت\n";
echo "3. التحكم الدقيق في أي ويدجت يراها كل دور\n";
echo "4. إنشاء أدوار جديدة مع صلاحيات ويدجت مخصصة\n";

echo "\n";
