{"version": 3, "names": ["default", "createNativeStackNavigator", "NativeStackView", "useHeaderHeight", "HeaderHeightContext", "useAnimatedHeaderHeight", "AnimatedHeaderHeightContext"], "sourceRoot": "../../../src", "sources": ["native-stack/index.tsx"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,0BAA0B,QAAQ,yCAAyC;;AAE/F;AACA;AACA;AACA,SAASD,OAAO,IAAIE,eAAe,QAAQ,yBAAyB;;AAEpE;AACA;AACA;AACA,SAASF,OAAO,IAAIG,eAAe,QAAQ,yBAAyB;AACpE,SAASH,OAAO,IAAII,mBAAmB,QAAQ,6BAA6B;AAE5E,SAASJ,OAAO,IAAIK,uBAAuB,QAAQ,iCAAiC;AACpF,SAASL,OAAO,IAAIM,2BAA2B,QAAQ,qCAAqC;;AAE5F;AACA;AACA", "ignoreList": []}