{"version": 3, "names": ["React", "ScreenContext", "ReanimatedNativeStackScreen", "AnimatedScreen", "ReanimatedScreenWrapper", "Component", "ref", "setNativeProps", "props", "setRef", "onComponentRef", "render", "ReanimatedScreen", "isNativeStack", "createElement", "_extends", "ReanimatedScreenProvider", "Provider", "value", "children"], "sourceRoot": "../../../src", "sources": ["reanimated/ReanimatedScreenProvider.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAA6B,OAAO;AAEhD,SAASC,aAAa,QAAQ,sBAAsB;AAEpD,OAAOC,2BAA2B,MAAM,+BAA+B;AACvE,OAAOC,cAAc,MAAM,oBAAoB;AAE/C,MAAMC,uBAAuB,SAASJ,KAAK,CAACK,SAAS,CAAc;EACzDC,GAAG,GAAyC,IAAI;EAExDC,cAAcA,CAACC,KAAkB,EAAQ;IACvC,IAAI,CAACF,GAAG,EAAEC,cAAc,CAACC,KAAK,CAAC;EACjC;EAEAC,MAAM,GAAIH,GAAyC,IAAW;IAC5D,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACE,KAAK,CAACE,cAAc,GAAGJ,GAAG,CAAC;EAClC,CAAC;EAEDK,MAAMA,CAAA,EAAG;IACP,MAAMC,gBAAgB,GAAG,IAAI,CAACJ,KAAK,CAACK,aAAa,GAC7CX,2BAA2B,GAC3BC,cAAc;IAClB,oBACEH,KAAA,CAAAc,aAAA,CAACF,gBAAgB,EAAAG,QAAA,KACX,IAAI,CAACP,KAAK;MACd;MACAF,GAAG,EAAE,IAAI,CAACG;IAAO,EAClB,CAAC;EAEN;AACF;AAEA,eAAe,SAASO,wBAAwBA,CAC9CR,KAAiC,EACjC;EACA;IAAA;IACE;IACAR,KAAA,CAAAc,aAAA,CAACb,aAAa,CAACgB,QAAQ;MAACC,KAAK,EAAEd;IAA+B,GAC3DI,KAAK,CAACW,QACe;EAAC;AAE7B", "ignoreList": []}