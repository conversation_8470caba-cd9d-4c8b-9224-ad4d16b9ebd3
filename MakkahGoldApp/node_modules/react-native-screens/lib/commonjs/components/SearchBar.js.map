{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_react", "_interopRequireDefault", "require", "_utils", "_reactNative", "_SearchBarNativeComponent", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "has", "get", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "NativeSearchBar", "SearchBarNativeComponent", "NativeSearchBarCommands", "SearchBarNativeCommands", "SearchBar", "props", "forwardedRef", "searchBarRef", "React", "useRef", "useImperativeHandle", "blur", "_callMethodWithRef", "ref", "focus", "toggleCancelButton", "flag", "clearText", "setText", "text", "cancelSearch", "useCallback", "method", "current", "console", "warn", "isSearchBarAvailableForCurrentPlatform", "View", "createElement", "onSearchFocus", "onFocus", "onSearchBlur", "onBlur", "onSearchButtonPress", "onCancelButtonPress", "onChangeText", "_default", "forwardRef"], "sourceRoot": "../../../src", "sources": ["components/SearchBar.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAGA,IAAAG,yBAAA,GAAAC,uBAAA,CAAAJ,OAAA;AAM4C,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAT,OAAA,EAAAS,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAE,GAAA,CAAAL,CAAA,UAAAG,CAAA,CAAAG,GAAA,CAAAN,CAAA,OAAAO,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAtB,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAuB,wBAAA,WAAAC,CAAA,IAAAX,CAAA,oBAAAW,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAb,CAAA,EAAAW,CAAA,SAAAG,CAAA,GAAAL,CAAA,GAAAtB,MAAA,CAAAuB,wBAAA,CAAAV,CAAA,EAAAW,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAR,GAAA,IAAAQ,CAAA,CAAAC,GAAA,IAAA5B,MAAA,CAAAC,cAAA,CAAAmB,CAAA,EAAAI,CAAA,EAAAG,CAAA,IAAAP,CAAA,CAAAI,CAAA,IAAAX,CAAA,CAAAW,CAAA,YAAAJ,CAAA,CAAAhB,OAAA,GAAAS,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAY,GAAA,CAAAf,CAAA,EAAAO,CAAA,GAAAA,CAAA;AAAA,SAAAd,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAT,OAAA,EAAAS,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAA7B,MAAA,CAAA8B,MAAA,GAAA9B,MAAA,CAAA8B,MAAA,CAAAC,IAAA,eAAAX,CAAA,aAAAP,CAAA,MAAAA,CAAA,GAAAmB,SAAA,CAAAC,MAAA,EAAApB,CAAA,UAAAG,CAAA,GAAAgB,SAAA,CAAAnB,CAAA,YAAAE,CAAA,IAAAC,CAAA,OAAAS,cAAA,CAAAC,IAAA,CAAAV,CAAA,EAAAD,CAAA,MAAAK,CAAA,CAAAL,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAK,CAAA,KAAAS,QAAA,CAAAK,KAAA,OAAAF,SAAA,KAP5C;AAUA,MAAMG,eAG0B,GAC9BC,iCACuB;AACzB,MAAMC,uBAA8C,GAClDC,kCAAgD;AAalD,SAASC,SAASA,CAChBC,KAAqB,EACrBC,YAA0C,EAC1C;EACA,MAAMC,YAAY,GAAGC,cAAK,CAACC,MAAM,CAA2B,IAAI,CAAC;EAEjED,cAAK,CAACE,mBAAmB,CAACJ,YAAY,EAAE,OAAO;IAC7CK,IAAI,EAAEA,CAAA,KAAM;MACVC,kBAAkB,CAACC,GAAG,IAAIX,uBAAuB,CAACS,IAAI,CAACE,GAAG,CAAC,CAAC;IAC9D,CAAC;IACDC,KAAK,EAAEA,CAAA,KAAM;MACXF,kBAAkB,CAACC,GAAG,IAAIX,uBAAuB,CAACY,KAAK,CAACD,GAAG,CAAC,CAAC;IAC/D,CAAC;IACDE,kBAAkB,EAAGC,IAAa,IAAK;MACrCJ,kBAAkB,CAACC,GAAG,IACpBX,uBAAuB,CAACa,kBAAkB,CAACF,GAAG,EAAEG,IAAI,CACtD,CAAC;IACH,CAAC;IACDC,SAAS,EAAEA,CAAA,KAAM;MACfL,kBAAkB,CAACC,GAAG,IAAIX,uBAAuB,CAACe,SAAS,CAACJ,GAAG,CAAC,CAAC;IACnE,CAAC;IACDK,OAAO,EAAGC,IAAY,IAAK;MACzBP,kBAAkB,CAACC,GAAG,IAAIX,uBAAuB,CAACgB,OAAO,CAACL,GAAG,EAAEM,IAAI,CAAC,CAAC;IACvE,CAAC;IACDC,YAAY,EAAEA,CAAA,KAAM;MAClBR,kBAAkB,CAACC,GAAG,IAAIX,uBAAuB,CAACkB,YAAY,CAACP,GAAG,CAAC,CAAC;IACtE;EACF,CAAC,CAAC,CAAC;EAEH,MAAMD,kBAAkB,GAAGJ,cAAK,CAACa,WAAW,CACzCC,MAAwC,IAAK;IAC5C,MAAMT,GAAG,GAAGN,YAAY,CAACgB,OAAO;IAChC,IAAIV,GAAG,EAAE;MACPS,MAAM,CAACT,GAAG,CAAC;IACb,CAAC,MAAM;MACLW,OAAO,CAACC,IAAI,CACV,mEACF,CAAC;IACH;EACF,CAAC,EACD,CAAClB,YAAY,CACf,CAAC;EAED,IAAI,CAACmB,6CAAsC,EAAE;IAC3CF,OAAO,CAACC,IAAI,CACV,+DACF,CAAC;IACD,OAAOE,iBAAI;EACb;EAEA,oBACEzD,MAAA,CAAAD,OAAA,CAAA2D,aAAA,CAAC5B,eAAe,EAAAN,QAAA;IACdmB,GAAG,EAAEN;EAAa,GACdF,KAAK;IACTwB,aAAa,EAAExB,KAAK,CAACyB,OAA8C;IACnEC,YAAY,EAAE1B,KAAK,CAAC2B,MAA6C;IACjEC,mBAAmB,EACjB5B,KAAK,CAAC4B,mBACP;IACDC,mBAAmB,EACjB7B,KAAK,CAAC6B,mBACP;IACDC,YAAY,EAAE9B,KAAK,CAAC8B;EAAoD,EACzE,CAAC;AAEN;AAAC,IAAAC,QAAA,GAAArE,OAAA,CAAAE,OAAA,gBAEcuC,cAAK,CAAC6B,UAAU,CAAoCjC,SAAS,CAAC", "ignoreList": []}