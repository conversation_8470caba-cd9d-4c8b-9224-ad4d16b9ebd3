{"version": 3, "file": "Checkbox.d.ts", "sourceRoot": "", "sources": ["../../../../src/components/Checkbox/Checkbox.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,qBAAqB,EAAY,MAAM,cAAc,CAAC;AAK/D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAE7C,MAAM,MAAM,KAAK,GAAG;IAClB;;OAEG;IACH,MAAM,EAAE,SAAS,GAAG,WAAW,GAAG,eAAe,CAAC;IAClD;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB;;OAEG;IACH,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,qBAAqB,KAAK,IAAI,CAAC;IAC7C;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,QAAA,MAAM,QAAQ,wCAAyC,KAAK,sBAO3D,CAAC;AAEF,eAAe,QAAQ,CAAC;AAGxB,QAAA,MAAM,iBAAiB,wCAZgC,KAAK,sBAY1B,CAAC;AAEnC,OAAO,EAAE,iBAAiB,IAAI,QAAQ,EAAE,CAAC"}