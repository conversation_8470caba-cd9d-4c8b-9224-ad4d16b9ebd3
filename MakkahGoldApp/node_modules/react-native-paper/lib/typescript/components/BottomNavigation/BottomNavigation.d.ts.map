{"version": 3, "file": "BottomNavigation.d.ts", "sourceRoot": "", "sources": ["../../../../src/components/BottomNavigation/BottomNavigation.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EACL,QAAQ,EACR,UAAU,EACV,cAAc,EAEd,SAAS,EAGT,SAAS,EACV,MAAM,cAAc,CAAC;AAOtB,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAE7C,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,SAAS,CAAC;AAC1C,OAAO,EAAE,KAAK,IAAI,oBAAoB,EAAE,MAAM,oCAAoC,CAAC;AAEnF,MAAM,MAAM,SAAS,GAAG;IACtB,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,WAAW,CAAC,EAAE,UAAU,CAAC;IACzB,aAAa,CAAC,EAAE,UAAU,CAAC;IAC3B,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;IAClC;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,IAAI,CAAC,EAAE,OAAO,CAAC;CAChB,CAAC;AAEF,KAAK,eAAe,CAAC,KAAK,SAAS,SAAS,IAAI;IAC9C,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,KAAK,EAAE,CAAC;CACjB,CAAC;AAEF,KAAK,aAAa,GAAG;IACnB,gBAAgB,EAAE,OAAO,CAAC;IAC1B,cAAc,IAAI,IAAI,CAAC;CACxB,CAAC;AAEF,KAAK,cAAc,CAAC,KAAK,SAAS,SAAS,IAAI,oBAAoB,GAAG;IACpE,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,EAAE,KAAK,CAAC;IACb,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC;IAC1B,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,WAAW,CAAC,EAAE,UAAU,CAAC;CAC1B,CAAC;AAEF,MAAM,MAAM,KAAK,CAAC,KAAK,SAAS,SAAS,IAAI;IAC3C;;;;;;OAMG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,eAAe,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC;IACxC;;;OAGG;IACH,aAAa,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;IACvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACH,WAAW,EAAE,CAAC,KAAK,EAAE;QACnB,KAAK,EAAE,KAAK,CAAC;QACb,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;KAC/B,KAAK,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;IAC7B;;OAEG;IACH,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE;QACnB,KAAK,EAAE,KAAK,CAAC;QACb,OAAO,EAAE,OAAO,CAAC;QACjB,KAAK,EAAE,MAAM,CAAC;KACf,KAAK,KAAK,CAAC,SAAS,CAAC;IACtB;;OAEG;IACH,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE;QACpB,KAAK,EAAE,KAAK,CAAC;QACb,OAAO,EAAE,OAAO,CAAC;QACjB,KAAK,EAAE,MAAM,CAAC;KACf,KAAK,KAAK,CAAC,SAAS,CAAC;IACtB;;;OAGG;IACH,eAAe,CAAC,EAAE,CAAC,KAAK,EAAE,cAAc,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,SAAS,CAAC;IACpE;;;OAGG;IACH,qBAAqB,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,KAAK,EAAE,KAAK,CAAA;KAAE,KAAK,MAAM,GAAG,SAAS,CAAC;IACxE;;OAEG;IACH,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,KAAK,EAAE,KAAK,CAAA;KAAE,KAAK,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IAC9E;;OAEG;IACH,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,KAAK,EAAE,KAAK,CAAA;KAAE,KAAK,MAAM,GAAG,SAAS,CAAC;IAC3D;;OAEG;IACH,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,KAAK,EAAE,KAAK,CAAA;KAAE,KAAK,MAAM,GAAG,SAAS,CAAC;IAC/D;;OAEG;IACH,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,KAAK,EAAE,KAAK,CAAA;KAAE,KAAK,OAAO,GAAG,SAAS,CAAC;IAC3D;;OAEG;IACH,SAAS,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,KAAK,EAAE,KAAK,CAAA;KAAE,KAAK,MAAM,GAAG,SAAS,CAAC;IAC5D;;OAEG;IACH,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,KAAK,EAAE,KAAK,CAAA;KAAE,GAAG,aAAa,KAAK,IAAI,CAAC;IAC/D;;OAEG;IACH,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE;QAAE,KAAK,EAAE,KAAK,CAAA;KAAE,GAAG,aAAa,KAAK,IAAI,CAAC;IACnE;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB;;;;OAIG;IACH,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC;;;OAGG;IACH,kBAAkB,CAAC,EAAE,SAAS,GAAG,UAAU,CAAC;IAC5C;;OAEG;IACH,oBAAoB,CAAC,EAAE,cAAc,GAAG,SAAS,CAAC;IAClD;;;OAGG;IACH,0BAA0B,CAAC,EAAE,OAAO,CAAC;IACrC;;;OAGG;IACH,cAAc,CAAC,EAAE;QACf,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,IAAI,CAAC,EAAE,MAAM,CAAC;KACf,CAAC;IACF;;;;;;OAMG;IACH,QAAQ,CAAC,EAAE,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;IAC5D;;OAEG;IACH,0BAA0B,CAAC,EAAE,MAAM,CAAC;IACpC,KAAK,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAC7B,oBAAoB,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAC5C;;OAEG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,CAAC;AAQF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+CG;AACH,QAAA,MAAM,gBAAgB;;IA2RtB;;;;OAIG;;;;0BAIe,MAAM,KAAK,IAAI;;;;sBAQf,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;CAlBhC,CAAC;AAgCF,eAAe,gBAAgB,CAAC"}