import * as React from 'react';
import { Animated, StyleProp, TextStyle, Text } from 'react-native';
import type { VariantProp } from './types';
import type { ThemeProp } from '../../types';
type Props<T> = React.ComponentPropsWithRef<typeof Animated.Text> & {
    /**
     * Variant defines appropriate text styles for type role and its size.
     * Available variants:
     *
     *  Display: `displayLarge`, `displayMedium`, `displaySmall`
     *
     *  Headline: `headlineLarge`, `headlineMedium`, `headlineSmall`
     *
     *  Title: `titleLarge`, `titleMedium`, `titleSmall`
     *
     *  Label:  `labelLarge`, `labelMedium`, `labelSmall`
     *
     *  Body: `bodyLarge`, `bodyMedium`, `bodySmall`
     */
    variant?: VariantProp<T>;
    style?: StyleProp<TextStyle>;
    /**
     * @optional
     */
    theme?: ThemeProp;
};
/**
 * Animated text component which follows styles from the theme.
 *
 * @extends Text props https://reactnative.dev/docs/text#props
 */
declare const AnimatedText: import("../../utils/forwardRef").ForwardRefComponent<Text & HTMLElement, Props<never>>;
export declare const customAnimatedText: <T>() => (props: Props<T>) => JSX.Element;
export default AnimatedText;
//# sourceMappingURL=AnimatedText.d.ts.map