{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../src/react-navigation/types.tsx"], "names": [], "mappings": ";AAAA,OAAO,KAAK,EACV,UAAU,EACV,iBAAiB,EACjB,cAAc,EACd,aAAa,EACb,SAAS,EACT,gBAAgB,EAChB,kBAAkB,EACnB,MAAM,0BAA0B,CAAC;AAElC,OAAO,KAAK,gBAAgB,MAAM,iDAAiD,CAAC;AAEpF,MAAM,MAAM,mCAAmC,GAAG;IAChD;;OAEG;IACH,QAAQ,EAAE;QAAE,IAAI,EAAE,SAAS,CAAC;QAAC,iBAAiB,EAAE,IAAI,CAAA;KAAE,CAAC;IACvD;;OAEG;IACH,YAAY,EAAE,EAAE,CAAC;CAClB,CAAC;AAEF,MAAM,MAAM,kCAAkC,GAAG,iBAAiB,CAChE,aAAa,EACb,mCAAmC,CACpC,GACC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAElC,MAAM,MAAM,+BAA+B,CACzC,SAAS,SAAS,aAAa,EAC/B,SAAS,SAAS,MAAM,SAAS,GAAG,MAAM,SAAS,EACnD,WAAW,SAAS,MAAM,GAAG,SAAS,GAAG,SAAS,IAChD,cAAc,CAChB,SAAS,EACT,SAAS,EACT,WAAW,EACX,kBAAkB,CAAC,SAAS,CAAC,EAC7B,kCAAkC,EAClC,mCAAmC,CACpC,GACC,gBAAgB,CAAC,SAAS,CAAC,CAAC;AAE9B,MAAM,MAAM,4BAA4B,CACtC,SAAS,SAAS,aAAa,EAC/B,SAAS,SAAS,MAAM,SAAS,GAAG,MAAM,SAAS,EACnD,WAAW,SAAS,MAAM,GAAG,SAAS,GAAG,SAAS,IAChD;IACF,UAAU,EAAE,+BAA+B,CACzC,SAAS,EACT,SAAS,EACT,WAAW,CACZ,CAAC;IACF,KAAK,EAAE,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;CACxC,CAAC;AAEF,MAAM,MAAM,kCAAkC,GAAG;IAC/C;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,UAAU,CAAC,EACP,MAAM,GACN,CAAC,CAAC,KAAK,EAAE;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,KAAK,KAAK,CAAC,SAAS,CAAC,CAAC;IAEtE;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC;IAExC;;OAEG;IACH,wBAAwB,CAAC,EAAE,MAAM,CAAC;IAElC;;OAEG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;CAC7B,CAAC;AAEF,MAAM,MAAM,2BAA2B,GAAG,UAAU,CAClD,kCAAkC,EAClC,+BAA+B,CAAC,aAAa,CAAC,EAC9C,SAAS,CAAC,aAAa,CAAC,CACzB,CAAC;AAEF,MAAM,MAAM,8BAA8B,GAAG,MAAM,CACjD,MAAM,EACN,2BAA2B,CAC5B,CAAC;AAEF,MAAM,MAAM,iCAAiC,GAAG,OAAO,CACrD,IAAI,CACF,KAAK,CAAC,cAAc,CAAC,OAAO,gBAAgB,CAAC,EAC3C,iBAAiB,GACjB,eAAe,GACf,YAAY,GACZ,gBAAgB,GAChB,aAAa,GACb,aAAa,GACb,YAAY,GACZ,uBAAuB,GACvB,UAAU,GACV,UAAU,GACV,cAAc,GACd,WAAW,GACX,SAAS,CACZ,CACF,CAAC"}