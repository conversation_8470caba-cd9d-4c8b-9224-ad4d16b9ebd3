{"version": 3, "names": ["color", "getContrastingColor", "input", "light", "dark", "isLight"], "sourceRoot": "../../../src", "sources": ["utils/getContrastingColor.tsx"], "mappings": "AAEA,OAAOA,KAAK,MAAM,OAAO;AAEzB,eAAe,SAASC,mBAAmBA,CACzCC,KAAiB,EACjBC,KAAa,EACbC,IAAY,EACJ;EACR,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOF,KAAK,CAACE,KAAK,CAAC,CAACG,OAAO,CAAC,CAAC,GAAGD,IAAI,GAAGD,KAAK;EAC9C;EAEA,OAAOA,KAAK;AACd", "ignoreList": []}