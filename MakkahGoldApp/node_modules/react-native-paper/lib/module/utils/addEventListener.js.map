{"version": 3, "names": ["addEventListener", "<PERSON><PERSON><PERSON>", "rest", "eventName", "handler", "removed", "subscription", "remove", "_Module$removeEventLi", "_Module$remove", "removeEventListener", "call", "addListener"], "sourceRoot": "../../../src", "sources": ["utils/addEventListener.tsx"], "mappings": "AAKA,OAAO,SAASA,gBAAgBA,CAQ9BC,MAAS,EAAE,GAAGC,IAAgD,EAAE;EAChE,MAAM,CAACC,SAAS,EAAEC,OAAO,CAAC,GAAGF,IAAI;EAEjC,IAAIG,OAAO,GAAG,KAAK;EAEnB,MAAMC,YAAY,GAAGL,MAAM,CAACD,gBAAgB,CAACG,SAAS,EAAEC,OAAO,CAAC,IAAI;IAClEG,MAAM,EAAEA,CAAA,KAAM;MAAA,IAAAC,qBAAA,EAAAC,cAAA;MACZ,IAAIJ,OAAO,EAAE;QACX;MACF;MAEA,CAAAG,qBAAA,GAAAP,MAAM,CAACS,mBAAmB,cAAAF,qBAAA,eAA1BA,qBAAA,CAAAG,IAAA,CAAAV,MAAM,EAAuBE,SAAS,EAAEC,OAAO,CAAC;MAChD,CAAAK,cAAA,GAAAR,MAAM,CAACM,MAAM,cAAAE,cAAA,eAAbA,cAAA,CAAAE,IAAA,CAAAV,MAAM,EAAUE,SAAS,EAAEC,OAAO,CAAC;MACnCC,OAAO,GAAG,IAAI;IAChB;EACF,CAAC;EAED,OAAOC,YAAY;AACrB;AAEA,OAAO,SAASM,WAAWA,CAKzBX,MAAS,EAAE,GAAGC,IAA2C,EAAE;EAC3D,MAAM,CAACC,SAAS,EAAEC,OAAO,CAAC,GAAGF,IAAI;EAEjC,IAAIG,OAAO,GAAG,KAAK;EAEnB,MAAMC,YAAY,GAAGL,MAAM,CAACW,WAAW,CAACT,SAAS,EAAEC,OAAO,CAAC,IAAI;IAC7DG,MAAM,EAAEA,CAAA,KAAM;MACZ,IAAIF,OAAO,EAAE;QACX;MACF;MAEAJ,MAAM,CAACS,mBAAmB,CAACP,SAAS,EAAEC,OAAO,CAAC;MAC9CC,OAAO,GAAG,IAAI;IAChB;EACF,CAAC;EAED,OAAOC,YAAY;AACrB", "ignoreList": []}