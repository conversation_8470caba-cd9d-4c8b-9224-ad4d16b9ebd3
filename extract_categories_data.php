<?php

require_once 'vendor/autoload.php';

// تحميل Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 استخراج بيانات الفئات\n";
echo "=====================\n\n";

use Illuminate\Support\Facades\DB;

// استخراج جميع الفئات
$categories = DB::table('categories')
    ->select('*')
    ->orderBy('parent_id', 'asc')
    ->orderBy('sort_order', 'asc')
    ->get();

echo "📊 إجمالي الفئات: " . $categories->count() . "\n\n";

echo "<?php\n\n";
echo "// بيانات الفئات المستخرجة من قاعدة البيانات\n";
echo "return [\n";

foreach ($categories as $category) {
    echo "    [\n";
    echo "        'name_ar' => '" . addslashes($category->name_ar) . "',\n";
    echo "        'name_en' => '" . addslashes($category->name_en) . "',\n";
    echo "        'slug' => '" . addslashes($category->slug) . "',\n";
    echo "        'description_ar' => " . ($category->description_ar ? "'" . addslashes($category->description_ar) . "'" : "null") . ",\n";
    echo "        'description_en' => " . ($category->description_en ? "'" . addslashes($category->description_en) . "'" : "null") . ",\n";
    echo "        'parent_id' => " . ($category->parent_id ? $category->parent_id : "null") . ",\n";
    echo "        'sort_order' => " . ($category->sort_order ?? 0) . ",\n";
    echo "        'is_active' => " . ($category->is_active ? 'true' : 'false') . ",\n";
    echo "        'is_featured' => " . ($category->is_featured ? 'true' : 'false') . ",\n";
    echo "        'meta_title_ar' => " . ($category->meta_title_ar ? "'" . addslashes($category->meta_title_ar) . "'" : "null") . ",\n";
    echo "        'meta_title_en' => " . ($category->meta_title_en ? "'" . addslashes($category->meta_title_en) . "'" : "null") . ",\n";
    echo "        'meta_description_ar' => " . ($category->meta_description_ar ? "'" . addslashes($category->meta_description_ar) . "'" : "null") . ",\n";
    echo "        'meta_description_en' => " . ($category->meta_description_en ? "'" . addslashes($category->meta_description_en) . "'" : "null") . ",\n";
    echo "        'created_at' => now(),\n";
    echo "        'updated_at' => now(),\n";
    echo "    ],\n";
}

echo "];\n";

echo "\n";
