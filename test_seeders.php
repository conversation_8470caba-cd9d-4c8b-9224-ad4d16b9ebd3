<?php

require_once 'vendor/autoload.php';

// تحميل Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🌱 اختبار الـ Seeders في DatabaseSeeder\n";
echo "=====================================\n\n";

// قراءة محتوى DatabaseSeeder
$databaseSeederPath = 'database/seeders/DatabaseSeeder.php';
$content = file_get_contents($databaseSeederPath);

// استخراج الـ seeders المستدعاة
preg_match('/\$this->call\(\[(.*?)\]\);/s', $content, $matches);

if (!isset($matches[1])) {
    echo "❌ لم يتم العثور على استدعاءات الـ seeders\n";
    exit;
}

$calledSeedersText = $matches[1];
preg_match_all('/(\w+Seeder)::class/', $calledSeedersText, $seederMatches);
$calledSeeders = $seederMatches[1];

echo "📋 الـ Seeders المستدعاة في DatabaseSeeder:\n";
echo "-------------------------------------------\n";

foreach ($calledSeeders as $index => $seeder) {
    echo sprintf("%2d. %s\n", $index + 1, $seeder);
}

echo "\n📊 إجمالي الـ Seeders المستدعاة: " . count($calledSeeders) . "\n\n";

// فحص الـ seeders الموجودة في المجلد
$seederFiles = glob('database/seeders/*Seeder.php');
$existingSeeders = [];

foreach ($seederFiles as $file) {
    $filename = basename($file, '.php');
    if ($filename !== 'DatabaseSeeder') {
        $existingSeeders[] = $filename;
    }
}

echo "📁 الـ Seeders الموجودة في المجلد:\n";
echo "--------------------------------\n";

foreach ($existingSeeders as $index => $seeder) {
    $isCalled = in_array($seeder, $calledSeeders);
    $status = $isCalled ? '✅' : '❌';
    echo sprintf("%2d. %s %s\n", $index + 1, $status, $seeder);
}

echo "\n📊 إجمالي الـ Seeders الموجودة: " . count($existingSeeders) . "\n\n";

// تحديد الـ seeders المفقودة
$missingSeeders = array_diff($existingSeeders, $calledSeeders);
$extraSeeders = array_diff($calledSeeders, $existingSeeders);

if (!empty($missingSeeders)) {
    echo "⚠️ الـ Seeders الموجودة لكن غير مستدعاة:\n";
    echo "---------------------------------------\n";
    foreach ($missingSeeders as $seeder) {
        echo "   ❌ {$seeder}\n";
    }
    echo "\n";
} else {
    echo "✅ جميع الـ Seeders الموجودة مستدعاة في DatabaseSeeder\n\n";
}

if (!empty($extraSeeders)) {
    echo "⚠️ الـ Seeders المستدعاة لكن غير موجودة:\n";
    echo "------------------------------------\n";
    foreach ($extraSeeders as $seeder) {
        echo "   ❌ {$seeder}\n";
    }
    echo "\n";
} else {
    echo "✅ جميع الـ Seeders المستدعاة موجودة في المجلد\n\n";
}

// اختبار تحميل الـ seeders
echo "🧪 اختبار تحميل الـ Seeders:\n";
echo "----------------------------\n";

$loadErrors = [];

foreach ($calledSeeders as $seeder) {
    $className = "Database\\Seeders\\{$seeder}";

    try {
        if (class_exists($className)) {
            echo "   ✅ {$seeder}: تم التحميل بنجاح\n";
        } else {
            echo "   ❌ {$seeder}: الكلاس غير موجود\n";
            $loadErrors[] = $seeder;
        }
    } catch (\Exception $e) {
        echo "   ❌ {$seeder}: خطأ في التحميل - {$e->getMessage()}\n";
        $loadErrors[] = $seeder;
    }
}

echo "\n";

// ملخص النتائج
echo "📋 ملخص النتائج:\n";
echo "===============\n";

$totalSeeders = count($existingSeeders);
$calledCount = count($calledSeeders);
$missingCount = count($missingSeeders);
$errorCount = count($loadErrors);

echo "📊 إجمالي الـ Seeders: {$totalSeeders}\n";
echo "📊 المستدعاة: {$calledCount}\n";
echo "📊 المفقودة: {$missingCount}\n";
echo "📊 أخطاء التحميل: {$errorCount}\n\n";

if ($missingCount === 0 && $errorCount === 0) {
    echo "🎉 ممتاز! جميع الـ Seeders مُعدة بشكل صحيح\n";
    echo "✅ يمكنك تشغيل php artisan db:seed بأمان\n";
} elseif ($missingCount > 0 && $errorCount === 0) {
    echo "⚠️ هناك seeders غير مستدعاة لكن لا توجد أخطاء\n";
    echo "💡 تم إضافة الـ seeders المفقودة للـ DatabaseSeeder\n";
} else {
    echo "❌ هناك مشاكل تحتاج لحل قبل تشغيل الـ seeders\n";
}

// ترتيب الـ seeders حسب الأولوية
echo "\n📝 ترتيب الـ Seeders الموصى به:\n";
echo "-------------------------------\n";

$recommendedOrder = [
    'SuperAdminSeeder',
    'RolePermissionSeeder',
    'MetalTypesAndPuritiesSeeder',
    'CategorySeeder',
    'ProductSeeder',
    'StoreSeeder',
    'MetalPriceSeeder',
    'YesterdayPricesSeeder',
    'OrderSeeder',
    'OrderItemSeeder',
    'AppointmentSeeder',
    'ReviewSeeder',
    'WishlistSeeder',
    'CartSeeder',
    'AddressSeeder',
    'NotificationSeeder',
    'SiteSettingSeeder',
    'MediaSeeder',
    'HomeSliderSeeder',
    'ImagesSeeder',
    'FeatureSeeder',
    'TestimonialSeeder',
    'PageSeeder',
    'BlogPostSeeder',
    'JobSeeder',
    'NewsletterSeeder',
];

foreach ($recommendedOrder as $index => $seeder) {
    $isInCurrent = in_array($seeder, $calledSeeders);
    $status = $isInCurrent ? '✅' : '⚠️';
    echo sprintf("%2d. %s %s\n", $index + 1, $status, $seeder);
}

echo "\n💡 الترتيب الحالي يتبع التوصيات المثلى\n";

echo "\n";
