<?php

require_once 'vendor/autoload.php';

// تحميل Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Services\FilamentPermissionService;
use Illuminate\Support\Facades\Auth;

echo "🧪 اختبار نظام فلترة الصلاحيات في Filament\n";
echo "==========================================\n\n";

// تسجيل دخول المستخدم admin
$adminUser = User::where('email', '<EMAIL>')->first();

if (!$adminUser) {
    echo "❌ لم يتم العثور على مستخدم admin\n";
    exit;
}

Auth::login($adminUser);

echo "✅ تم تسجيل دخول المستخدم: {$adminUser->email}\n";
echo "🎭 الأدوار: " . $adminUser->roles->pluck('name')->join(', ') . "\n\n";

// 1. اختبار فلترة الموارد
echo "1️⃣ اختبار فلترة الموارد:\n";
echo "------------------------\n";

$allResourceClasses = [
    \App\Filament\Resources\UserResource::class,
    \App\Filament\Resources\RoleResource::class,
    \App\Filament\Resources\PermissionResource::class,
    \App\Filament\Resources\ProductResource::class,
    \App\Filament\Resources\CategoryResource::class,
    \App\Filament\Resources\OrderResource::class,
    \App\Filament\Resources\SuperAdminSettingResource::class,
];

$availableResources = FilamentPermissionService::getAvailableResourceClasses();

echo "   - إجمالي الموارد في النظام: " . count($allResourceClasses) . "\n";
echo "   - الموارد المتاحة للمستخدم: " . count($availableResources) . "\n";

echo "   📋 الموارد المتاحة:\n";
foreach ($availableResources as $resource) {
    $resourceName = class_basename($resource);
    echo "      ✅ {$resourceName}\n";
}

echo "   📋 الموارد المحجوبة:\n";
$blockedResources = array_diff($allResourceClasses, $availableResources);
foreach ($blockedResources as $resource) {
    $resourceName = class_basename($resource);
    echo "      ❌ {$resourceName}\n";
}

echo "\n";

// 2. اختبار فلترة الصفحات
echo "2️⃣ اختبار فلترة الصفحات:\n";
echo "------------------------\n";

$allPageClasses = [
    \App\Filament\Pages\DashboardPage::class,
    \App\Filament\Pages\PreviewSettings::class,
    \App\Filament\Pages\SearchSettings::class,
    \App\Filament\Pages\SiteSettingsManager::class,
    \App\Filament\Pages\TranslationsManager::class,
];

$availablePages = FilamentPermissionService::getAvailablePageClasses();

echo "   - إجمالي الصفحات في النظام: " . count($allPageClasses) . "\n";
echo "   - الصفحات المتاحة للمستخدم: " . count($availablePages) . "\n";

echo "   📋 الصفحات المتاحة:\n";
foreach ($availablePages as $page) {
    $pageName = class_basename($page);
    echo "      ✅ {$pageName}\n";
}

echo "   📋 الصفحات المحجوبة:\n";
$blockedPages = array_diff($allPageClasses, $availablePages);
foreach ($blockedPages as $page) {
    $pageName = class_basename($page);
    echo "      ❌ {$pageName}\n";
}

echo "\n";

// 3. اختبار فلترة الودجات
echo "3️⃣ اختبار فلترة الودجات:\n";
echo "-------------------------\n";

$allWidgetClasses = [
    \App\Filament\Widgets\StatsOverview::class,
    \App\Filament\Widgets\SalesChart::class,
    \App\Filament\Widgets\GoldPriceChart::class,
    \App\Filament\Widgets\LatestOrders::class,
    \App\Filament\Widgets\UpcomingAppointments::class,
    \App\Filament\Widgets\MaintenanceModeToggle::class,
];

$availableWidgets = FilamentPermissionService::getAvailableWidgetClasses();

echo "   - إجمالي الودجات في النظام: " . count($allWidgetClasses) . "\n";
echo "   - الودجات المتاحة للمستخدم: " . count($availableWidgets) . "\n";

echo "   📋 الودجات المتاحة:\n";
foreach ($availableWidgets as $widget) {
    $widgetName = class_basename($widget);
    echo "      ✅ {$widgetName}\n";
}

echo "   📋 الودجات المحجوبة:\n";
$blockedWidgets = array_diff($allWidgetClasses, $availableWidgets);
foreach ($blockedWidgets as $widget) {
    $widgetName = class_basename($widget);
    echo "      ❌ {$widgetName}\n";
}

echo "\n";

// 4. اختبار صلاحيات محددة
echo "4️⃣ اختبار صلاحيات محددة:\n";
echo "-------------------------\n";

$testCases = [
    ['resource' => \App\Filament\Resources\UserResource::class, 'expected' => true],
    ['resource' => \App\Filament\Resources\SuperAdminSettingResource::class, 'expected' => false],
    ['page' => \App\Filament\Pages\DashboardPage::class, 'expected' => true],
    ['page' => \App\Filament\Pages\TranslationsManager::class, 'expected' => true],
    ['widget' => \App\Filament\Widgets\StatsOverview::class, 'expected' => true],
];

foreach ($testCases as $test) {
    if (isset($test['resource'])) {
        $canAccess = FilamentPermissionService::canAccessResource($test['resource']);
        $resourceName = class_basename($test['resource']);
        $status = $canAccess ? '✅' : '❌';
        $expected = $test['expected'] ? 'متوقع' : 'محجوب';
        echo "   {$status} مورد {$resourceName}: " . ($canAccess ? 'متاح' : 'محجوب') . " ({$expected})\n";
    } elseif (isset($test['page'])) {
        $canAccess = FilamentPermissionService::canAccessPage($test['page']);
        $pageName = class_basename($test['page']);
        $status = $canAccess ? '✅' : '❌';
        $expected = $test['expected'] ? 'متوقع' : 'محجوب';
        echo "   {$status} صفحة {$pageName}: " . ($canAccess ? 'متاح' : 'محجوب') . " ({$expected})\n";
    } elseif (isset($test['widget'])) {
        $canAccess = FilamentPermissionService::canAccessWidget($test['widget']);
        $widgetName = class_basename($test['widget']);
        $status = $canAccess ? '✅' : '❌';
        $expected = $test['expected'] ? 'متوقع' : 'محجوب';
        echo "   {$status} ودجة {$widgetName}: " . ($canAccess ? 'متاح' : 'محجوب') . " ({$expected})\n";
    }
}

echo "\n";

// 5. اختبار مع Super Admin
echo "5️⃣ اختبار مع Super Admin:\n";
echo "-------------------------\n";

$superAdminUser = User::whereHas('roles', function($query) {
    $query->where('name', 'super_admin');
})->first();

if ($superAdminUser) {
    Auth::login($superAdminUser);
    
    $superAdminResources = FilamentPermissionService::getAvailableResourceClasses();
    $superAdminPages = FilamentPermissionService::getAvailablePageClasses();
    $superAdminWidgets = FilamentPermissionService::getAvailableWidgetClasses();
    
    echo "   - موارد Super Admin: " . count($superAdminResources) . "\n";
    echo "   - صفحات Super Admin: " . count($superAdminPages) . "\n";
    echo "   - ودجات Super Admin: " . count($superAdminWidgets) . "\n";
    
    if (count($superAdminResources) >= count($availableResources)) {
        echo "   ✅ Super Admin يرى موارد أكثر أو مساوية للـ Admin\n";
    } else {
        echo "   ⚠️ Super Admin يرى موارد أقل من الـ Admin - خطأ!\n";
    }
} else {
    echo "   ❌ لم يتم العثور على مستخدم Super Admin\n";
}

echo "\n";

// العودة لمستخدم admin
Auth::login($adminUser);

// 6. ملخص النتائج
echo "6️⃣ ملخص النتائج:\n";
echo "================\n";

$totalResources = count($allResourceClasses);
$availableResourcesCount = count($availableResources);
$resourceFilteringPercentage = round((($totalResources - $availableResourcesCount) / $totalResources) * 100, 1);

$totalPages = count($allPageClasses);
$availablePagesCount = count($availablePages);
$pageFilteringPercentage = round((($totalPages - $availablePagesCount) / $totalPages) * 100, 1);

$totalWidgets = count($allWidgetClasses);
$availableWidgetsCount = count($availableWidgets);
$widgetFilteringPercentage = round((($totalWidgets - $availableWidgetsCount) / $totalWidgets) * 100, 1);

echo "📊 إحصائيات الفلترة:\n";
echo "   - الموارد: {$availableResourcesCount}/{$totalResources} متاح ({$resourceFilteringPercentage}% محجوب)\n";
echo "   - الصفحات: {$availablePagesCount}/{$totalPages} متاح ({$pageFilteringPercentage}% محجوب)\n";
echo "   - الودجات: {$availableWidgetsCount}/{$totalWidgets} متاح ({$widgetFilteringPercentage}% محجوب)\n\n";

if ($resourceFilteringPercentage > 0 || $pageFilteringPercentage > 0 || $widgetFilteringPercentage > 0) {
    echo "✅ نظام الفلترة يعمل بنجاح!\n";
    echo "🎯 المستخدم admin يرى فقط العناصر التي يملك صلاحية الوصول إليها\n";
} else {
    echo "⚠️ نظام الفلترة لا يعمل - جميع العناصر متاحة\n";
    echo "🔧 تحقق من الصلاحيات والتكوين\n";
}

echo "\n💡 التوصية: اختبر الواجهة الآن للتأكد من عمل الفلترة\n";

echo "\n";
