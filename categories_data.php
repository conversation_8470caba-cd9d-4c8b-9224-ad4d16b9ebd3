🔍 استخراج بيانات الفئات
=====================


   Illuminate\Database\QueryException 

  SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sort_order' in 'order clause' (Connection: mysql, SQL: select * from `categories` order by `parent_id` asc, `sort_order` asc)

  at vendor/laravel/framework/src/Illuminate/Database/Connection.php:822
    818▕                     $this->getName(), $query, $this->prepareBindings($bindings), $e
    819▕                 );
    820▕             }
    821▕ 
  ➜ 822▕             throw new QueryException(
    823▕                 $this->getName(), $query, $this->prepareBindings($bindings), $e
    824▕             );
    825▕         }
    826▕     }

      [2m+9 vendor frames [22m

  10  extract_categories_data.php:19
      Illuminate\Database\Query\Builder::get()

