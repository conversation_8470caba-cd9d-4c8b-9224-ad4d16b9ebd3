<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Language;
use Illuminate\Support\Facades\DB;

class LanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (Language::count() > 0) {
            $this->command->info('Languages already exist. Skipping...');
            return;
        }

        $this->command->info('Seeding languages...');

        $languages = [
            [
                'code' => 'ar',
                'name' => 'العربية',
                'native_name' => 'العربية',
                'is_rtl' => true,
                'is_default' => true,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'code' => 'en',
                'name' => 'English',
                'native_name' => 'English',
                'is_rtl' => false,
                'is_default' => false,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('languages')->insert($languages);

        $this->command->info('Languages seeded successfully!');
    }
}
