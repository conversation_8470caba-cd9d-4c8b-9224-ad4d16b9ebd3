<?php

namespace Database\Seeders;

use App\Models\Newsletter;
use Illuminate\Database\Seeder;

class NewsletterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // التحقق من وجود اشتراكات نشرة بريدية مسبقاً
        if (Newsletter::count() > 0) {
            $this->command->info('اشتراكات النشرة البريدية موجودة مسبقاً، تم تخطي الإنشاء.');
            return;
        }
        $this->command->info('جاري إنشاء مشتركي النشرة البريدية التجريبيين...');

        $subscribers = [
            [
                'email' => '<EMAIL>',
                'name' => 'أحمد محمد',
                'is_active' => true,
                'subscribed_at' => now()->subDays(30),
            ],
            [
                'email' => '<EMAIL>',
                'name' => 'سارة أحمد',
                'is_active' => true,
                'subscribed_at' => now()->subDays(25),
            ],
            [
                'email' => '<EMAIL>',
                'name' => 'محمد علي',
                'is_active' => true,
                'subscribed_at' => now()->subDays(20),
            ],
            [
                'email' => '<EMAIL>',
                'name' => 'نورا حسن',
                'is_active' => false,
                'subscribed_at' => now()->subDays(15),
                'unsubscribed_at' => now()->subDays(5),
            ],
            [
                'email' => '<EMAIL>',
                'name' => 'خالد عبدالله',
                'is_active' => true,
                'subscribed_at' => now()->subDays(10),
            ],
        ];

        foreach ($subscribers as $subscriber) {
            Newsletter::create($subscriber);
        }

        $this->command->info('تم إنشاء ' . count($subscribers) . ' مشتركين في النشرة البريدية بنجاح!');
    }
}
