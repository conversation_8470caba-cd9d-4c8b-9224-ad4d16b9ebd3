<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\DB;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (Permission::count() > 0) {
            $this->command->info('Permissions already exist. Skipping...');
            return;
        }

        $this->command->info('Seeding permissions...');

        // استخراج الصلاحيات من قاعدة البيانات الحالية
        $existingPermissions = $this->getExistingPermissions();

        if (empty($existingPermissions)) {
            // إنشاء صلاحيات أساسية إذا لم توجد
            $existingPermissions = $this->getBasicPermissions();
        }

        // إدراج الصلاحيات على دفعات لتحسين الأداء
        $chunks = array_chunk($existingPermissions, 50);

        foreach ($chunks as $chunk) {
            DB::table('permissions')->insert($chunk);
        }

        $this->command->info('Permissions seeded successfully! Total: ' . count($existingPermissions));
    }

    /**
     * الحصول على الصلاحيات الموجودة من قاعدة البيانات
     */
    private function getExistingPermissions(): array
    {
        try {
            $permissions = DB::table('permissions')
                ->select('name', 'guard_name')
                ->get()
                ->map(function ($permission) {
                    return [
                        'name' => $permission->name,
                        'guard_name' => $permission->guard_name,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                })
                ->toArray();

            return $permissions;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * الحصول على صلاحيات أساسية في حالة عدم وجود بيانات
     */
    private function getBasicPermissions(): array
    {
        $resources = [
            'user', 'role', 'permission', 'category', 'product', 'order',
            'appointment', 'store', 'job', 'newsletter', 'review',
            'metal::price', 'metal::type', 'metal::purity', 'site::setting'
        ];

        $actions = ['view_any', 'create', 'update', 'delete'];
        $permissions = [];

        foreach ($resources as $resource) {
            foreach ($actions as $action) {
                $permissions[] = [
                    'name' => $action . '_' . $resource,
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }

        return $permissions;
    }
}
