<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Make sure we have users
        if (User::role('user')->count() === 0) {
            $this->call(DatabaseSeeder::class);
        }

        // Get customer IDs
        $customerIds = User::role('user')->pluck('id')->toArray();

        // Create orders with different statuses
        $statuses = ['pending', 'processing', 'completed', 'cancelled', 'refunded'];
        $paymentStatuses = ['pending', 'paid', 'failed', 'refunded'];
        $paymentMethods = ['cash', 'credit_card', 'bank_transfer'];

        // حذف الطلبات الموجودة
        try {
            // حذف عناصر الطلبات أولاً
            \DB::statement('SET FOREIGN_KEY_CHECKS=0;');
            \DB::table('order_items')->truncate();
            \DB::table('orders')->truncate();
            \DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        } catch (\Exception $e) {
            $this->command->warn('لم يتم حذف الطلبات الموجودة: ' . $e->getMessage());
        }

        // إنشاء 50 طلب جديد
        if (count($customerIds) > 0) {
            for ($i = 0; $i < 50; $i++) {
                $status = $statuses[array_rand($statuses)];
                $paymentStatus = $paymentStatuses[array_rand($paymentStatuses)];

                // إذا كان الطلب مكتمل، يجب أن تكون حالة الدفع مدفوعة
                if ($status === 'completed') {
                    $paymentStatus = 'paid';
                }

                // إذا فشل الدفع، يجب أن يكون الطلب معلقًا أو ملغيًا
                if ($paymentStatus === 'failed') {
                    $status = rand(0, 1) ? 'pending' : 'cancelled';
                }

                $createdAt = now()->subDays(rand(1, 60));

                Order::create([
                    'user_id' => $customerIds[array_rand($customerIds)],
                    'store_id' => rand(1, 5), // افتراض وجود 5 متاجر
                    'order_number' => 'ORD-' . date('Ymd') . '-' . str_pad($i + 1, 4, '0', STR_PAD_LEFT),
                    'status' => $status,
                    'payment_status' => $paymentStatus,
                    'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                    'total_amount' => 0, // سيتم تحديثه لاحقًا في OrderItemSeeder
                    'tax_amount' => 0,
                    'shipping_amount' => 0,
                    'discount_amount' => 0,
                    'currency' => 'EGP',
                    'payment_transaction_id' => 'TXN-' . strtoupper(substr(md5(rand()), 0, 8)),
                    'shipping_name' => 'عميل تجريبي',
                    'shipping_address' => 'عنوان تجريبي، شارع ' . rand(1, 100),
                    'shipping_city' => 'القاهرة',
                    'shipping_country' => 'مصر',
                    'shipping_phone' => '+2' . rand(1000000000, 1999999999),
                    'shipping_email' => 'customer' . rand(1, 1000) . '@example.com',
                    'shipping_postal_code' => rand(10000, 99999),
                    'notes' => 'طلب تجريبي #' . ($i + 1),
                    'is_guest' => false,
                    'created_at' => $createdAt,
                    'updated_at' => $createdAt->addHours(rand(1, 24)),
                ]);
            }

            $this->command->info('تم إنشاء ' . Order::count() . ' طلب تجريبي بنجاح!');

            // تشغيل بذرة عناصر الطلبات
            $this->call(OrderItemSeeder::class);
        }
    }
}
