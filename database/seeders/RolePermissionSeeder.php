<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // تحقق من وجود الصلاحيات المنشأة بواسطة Shield
        if (Permission::count() === 0) {
            echo "لا توجد صلاحيات في النظام. يرجى تشغيل 'php artisan shield:generate --all' أولاً.\n";
            return;
        }

        // إنشاء الأدوار الأساسية إذا لم تكن موجودة
        $roles = [
            'super_admin' => 'سوبر أدمن',
            'admin' => 'مدير',
            'manager' => 'مشرف',
            'user' => 'مستخدم',
        ];

        foreach ($roles as $roleName => $roleLabel) {
            Role::firstOrCreate([
                'name' => $roleName,
                'guard_name' => 'web'
            ]);
        }

        // الصلاحيات يتم إنشاؤها تلقائياً بواسطة Shield
        // لذلك لا نحتاج لإنشائها هنا

        // تعيين الصلاحيات للأدوار
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $adminRole = Role::where('name', 'admin')->first();
        $managerRole = Role::where('name', 'manager')->first();
        $userRole = Role::where('name', 'user')->first();

        // السوبر أدمن يحصل على جميع الصلاحيات
        if ($superAdminRole) {
            $superAdminRole->syncPermissions(Permission::all());
        }

        // المدير يحصل على معظم الصلاحيات عدا إعدادات السوبر أدمن
        if ($adminRole) {
            $adminPermissions = Permission::whereNotIn('name', [
                'view_any_super::admin::setting',
                'create_super::admin::setting',
                'update_super::admin::setting',
                'delete_super::admin::setting',
                'delete_role',
            ])->get();
            $adminRole->syncPermissions($adminPermissions);
        }

        // المشرف يحصل على صلاحيات محدودة (الصلاحيات المبسطة)
        if ($managerRole) {
            $managerPermissions = Permission::whereIn('name', [
                'view_any_product', 'create_product', 'update_product',
                'view_any_category', 'create_category', 'update_category',
                'view_any_order', 'update_order',
                'view_any_appointment', 'update_appointment',
                'view_any_metal::price', 'create_metal::price', 'update_metal::price',
                'view_any_store',
                'page_DashboardPage',
                'widget_StatsOverview',
                'widget_LatestOrders',
                'widget_UpcomingAppointments',
            ])->get();
            $managerRole->syncPermissions($managerPermissions);
        }

        // المستخدم العادي لا يحصل على صلاحيات إدارية
        if ($userRole) {
            $userRole->syncPermissions([]);
        }

        // تحديث المستخدمين الموجودين لاستخدام الأدوار الجديدة
        $this->migrateExistingUsers();
    }

    /**
     * تعيين الأدوار للمستخدمين الموجودين
     */
    private function migrateExistingUsers()
    {
        // تعيين دور للمستخدمين الذين لا يملكون أدوار
        $usersWithoutRoles = User::doesntHave('roles')->get();

        foreach ($usersWithoutRoles as $user) {
            // تعيين دور حسب البريد الإلكتروني
            if (str_contains($user->email, 'admin')) {
                $user->assignRole('admin');
            } elseif (str_contains($user->email, 'manager')) {
                $user->assignRole('manager');
            } elseif (str_contains($user->email, 'superadmin')) {
                $user->assignRole('super_admin');
            } else {
                $user->assignRole('user');
            }
        }
    }
}
