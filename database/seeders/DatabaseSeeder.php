<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user if not exists
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::factory()->create([
                'name' => 'مدير النظام',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'admin',
            ]);
        }

        // Create manager user if not exists
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::factory()->create([
                'name' => 'مدير المتجر',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'manager',
            ]);
        }

        // Create regular customers (only if we have less than 10)
        if (User::role('user')->count() < 10) {
            $count = 10 - User::role('user')->count();
            $users = User::factory($count)->create();

            // تعيين دور 'user' للمستخدمين الجدد
            foreach ($users as $user) {
                $user->assignRole('user');
            }
        }

        // Seed all data
        $this->call([
            SuperAdminSeeder::class, // إنشاء السوبر أدمن أولاً
            CategorySeeder::class,
            ProductSeeder::class,
            StoreSeeder::class,
            MetalPriceSeeder::class,
            OrderSeeder::class,
            OrderItemSeeder::class,
            AppointmentSeeder::class,
            ReviewSeeder::class,
            WishlistSeeder::class,
            CartSeeder::class,
            AddressSeeder::class,
            NotificationSeeder::class,
            SiteSettingSeeder::class,
            MediaSeeder::class,
            HomeSliderSeeder::class,
            ImagesSeeder::class,
            FeatureSeeder::class,
            PageSeeder::class,
            BlogPostSeeder::class,
            JobSeeder::class,
        ]);
    }
}
