<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (User::count() > 0) {
            $this->command->info('Users already exist. Skipping...');
            return;
        }

        $this->command->info('Seeding users...');

        // إنشاء المستخدمين الأساسيين
        $users = [
            [
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'phone' => '+966500000001',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'phone' => '+966500000002',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Manager User',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'phone' => '+966500000003',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($users as $userData) {
            $user = User::create($userData);

            // تعيين الأدوار
            if ($userData['email'] === '<EMAIL>') {
                $user->assignRole('super_admin');
            } elseif ($userData['email'] === '<EMAIL>') {
                $user->assignRole('admin');
            } elseif ($userData['email'] === '<EMAIL>') {
                $user->assignRole('manager');
            }
        }

        $this->command->info('Users seeded successfully!');
    }
}
