🔍 استخراج بيانات الصلاحيات
========================

📊 إجمالي الصلاحيات: 118

<?php

// بيانات الصلاحيات المستخرجة من قاعدة البيانات
return [
    [
        'name' => 'create_appointment',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_blog::post',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_category',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_feature',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_home::slider',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_job',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_job::application',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_language',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_language::manager',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_metal::price',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_metal::purity',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_metal::type',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_newsletter',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_order',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_page',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_permission',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_product',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_role',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_setting::change',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_site::setting',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_store',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_super::admin::setting',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_testimonial',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'create_user',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_appointment',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_blog::post',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_category',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_feature',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_home::slider',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_job',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_job::application',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_language',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_language::manager',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_metal::price',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_metal::purity',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_metal::type',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_newsletter',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_order',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_page',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_permission',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_product',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_role',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_setting::change',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_site::setting',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_store',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_super::admin::setting',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_testimonial',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'delete_user',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'page_DashboardPage',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'page_PreviewSettings',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'page_SearchSettings',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'page_SiteSettingsManager',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'page_TranslationsManager',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_appointment',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_blog::post',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_category',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_feature',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_home::slider',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_job',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_job::application',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_language',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_language::manager',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_metal::price',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_metal::purity',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_metal::type',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_newsletter',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_order',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_page',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_permission',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_product',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_role',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_setting::change',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_site::setting',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_store',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_super::admin::setting',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_testimonial',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'update_user',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_appointment',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_blog::post',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_category',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_feature',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_home::slider',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_job',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_job::application',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_language',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_language::manager',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_metal::price',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_metal::purity',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_metal::type',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_newsletter',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_order',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_page',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_permission',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_product',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_review',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_role',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_setting::change',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_site::setting',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_store',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_super::admin::setting',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_testimonial',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_any_user',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_role',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'view_user',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_ContentStatsWidget',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_EngagementStatsWidget',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_GoldPriceChart',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_LatestOrders',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_MainStatsOverview',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_MaintenanceModeToggle',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_MetalPricesWidget',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_RecentOrdersWidget',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_SalesChart',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_SalesChartWidget',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_StatsOverview',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_SystemStatusWidget',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_UpcomingAppointments',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'name' => 'widget_UpcomingAppointmentsWidget',
        'guard_name' => 'web',
        'created_at' => now(),
        'updated_at' => now(),
    ],
];

