<?php

require_once 'vendor/autoload.php';

// تحميل Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 استخراج بيانات الصلاحيات\n";
echo "========================\n\n";

use Illuminate\Support\Facades\DB;

// استخراج جميع الصلاحيات
$permissions = DB::table('permissions')
    ->select('name', 'guard_name')
    ->orderBy('name')
    ->get();

echo "📊 إجمالي الصلاحيات: " . $permissions->count() . "\n\n";

echo "<?php\n\n";
echo "// بيانات الصلاحيات المستخرجة من قاعدة البيانات\n";
echo "return [\n";

foreach ($permissions as $permission) {
    echo "    [\n";
    echo "        'name' => '{$permission->name}',\n";
    echo "        'guard_name' => '{$permission->guard_name}',\n";
    echo "        'created_at' => now(),\n";
    echo "        'updated_at' => now(),\n";
    echo "    ],\n";
}

echo "];\n";

echo "\n";
