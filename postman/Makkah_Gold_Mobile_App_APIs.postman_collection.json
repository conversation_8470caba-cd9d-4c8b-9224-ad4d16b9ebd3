{"info": {"_postman_id": "makkah-gold-mobile-apis", "name": "Makkah Gold Mobile App APIs", "description": "مجموعة APIs مخصصة لتطبيق مكة الذهبية للموبايل", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "أسعار المعادن", "item": [{"name": "الحصول على أحدث أسعار المعادن", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/app/v1/metal-prices/current", "host": ["{{base_url}}"], "path": ["api", "app", "v1", "metal-prices", "current"]}, "description": "جلب أحدث أسعار جميع المعادن (ذهب، فضة) مع جميع العيارات"}, "response": []}, {"name": "الحصول على أسعار معدن محدد", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/app/v1/metal-prices/gold", "host": ["{{base_url}}"], "path": ["api", "app", "v1", "metal-prices", "gold"]}, "description": "جلب أسعار معدن محدد (gold, silver, platinum)"}, "response": []}, {"name": "تاريخ أسعار معدن وعيار محدد", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/app/v1/metal-prices/gold/24K/history?days=30", "host": ["{{base_url}}"], "path": ["api", "app", "v1", "metal-prices", "gold", "24K", "history"], "query": [{"key": "days", "value": "30", "description": "<PERSON><PERSON><PERSON> الأيام (افتراضي: 30)"}]}, "description": "جلب تاريخ أسعار معدن وعيار محدد مع الإحصائيات"}, "response": []}, {"name": "حساب قيمة المجوهرات", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"metal_type\": \"gold\",\n    \"purity\": \"21K\",\n    \"weight\": 10.5,\n    \"unit\": \"gram\"\n}"}, "url": {"raw": "{{base_url}}/api/app/v1/metal-prices/calculate-jewelry-value", "host": ["{{base_url}}"], "path": ["api", "app", "v1", "metal-prices", "calculate-jewelry-value"]}, "description": "حساب قيمة المجوهرات بناءً على الوزن والعيار والسعر الحالي"}, "response": []}], "description": "APIs خاصة بأسعار المعادن وحساب قيمة المجوهرات"}, {"name": "حاسبة الزكاة", "item": [{"name": "معلومات النصاب", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/app/v1/zakat/nisab-info", "host": ["{{base_url}}"], "path": ["api", "app", "v1", "zakat", "nisab-info"]}, "description": "الحصول على معلومات النصاب للذهب والفضة مع الأسعار الحالية"}, "response": []}, {"name": "حساب زكاة الذهب", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"items\": [\n        {\n            \"name\": \"خاتم ذهب\",\n            \"weight\": 10,\n            \"purity\": \"21K\"\n        },\n        {\n            \"name\": \"سلسلة ذهب\",\n            \"weight\": 25,\n            \"purity\": \"18K\"\n        },\n        {\n            \"name\": \"أساور ذهب\",\n            \"weight\": 50,\n            \"purity\": \"24K\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/app/v1/zakat/gold", "host": ["{{base_url}}"], "path": ["api", "app", "v1", "zakat", "gold"]}, "description": "حساب زكاة الذهب لعدة قطع مع عيارات مختلفة"}, "response": []}, {"name": "حساب زكاة الفضة", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"items\": [\n        {\n            \"name\": \"خاتم فضة\",\n            \"weight\": 15,\n            \"purity\": \"925\"\n        },\n        {\n            \"name\": \"سلسلة فضة\",\n            \"weight\": 30,\n            \"purity\": \"999\"\n        },\n        {\n            \"name\": \"أساور فضة\",\n            \"weight\": 600,\n            \"purity\": \"925\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/app/v1/zakat/silver", "host": ["{{base_url}}"], "path": ["api", "app", "v1", "zakat", "silver"]}, "description": "حساب زكاة الفضة لعدة قطع مع عيارات مختلفة"}, "response": []}], "description": "APIs خاصة بحساب زكاة الذهب والفضة"}, {"name": "الفئات", "item": [{"name": "جميع الفئات", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/app/v1/categories", "host": ["{{base_url}}"], "path": ["api", "app", "v1", "categories"]}, "description": "جلب جميع الفئات النشطة مع الفئات الفرعية وعدد المنتجات"}, "response": []}, {"name": "فئة محددة مع منتجاتها", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/app/v1/categories/1/products", "host": ["{{base_url}}"], "path": ["api", "app", "v1", "categories", "1", "products"]}, "description": "جلب فئة محددة مع جميع منتجاتها"}, "response": []}], "description": "APIs خاصة بالفئات والتصنيفات"}, {"name": "المنتجات", "item": [{"name": "جميع المنتجات", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/app/v1/products?per_page=20&sort_by=created_at&sort_direction=desc", "host": ["{{base_url}}"], "path": ["api", "app", "v1", "products"], "query": [{"key": "per_page", "value": "20", "description": "عد<PERSON> المنتجات في الصفحة"}, {"key": "sort_by", "value": "created_at", "description": "ترتيب حسب: created_at, price, name, weight"}, {"key": "sort_direction", "value": "desc", "description": "اتجاه الترتيب: asc, desc"}, {"key": "category_id", "value": "", "description": "فلترة حسب الفئة", "disabled": true}, {"key": "featured", "value": "", "description": "المنتجات المميزة فقط", "disabled": true}, {"key": "material_type", "value": "", "description": "نوع المعدن", "disabled": true}, {"key": "search", "value": "", "description": "البحث في الاسم والوصف", "disabled": true}]}, "description": "جل<PERSON> جميع المنتجات مع إمكانية الفلترة والترتيب"}, "response": []}, {"name": "من<PERSON><PERSON> محدد", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/app/v1/products/1", "host": ["{{base_url}}"], "path": ["api", "app", "v1", "products", "1"]}, "description": "جلب تفاصيل منتج محدد"}, "response": []}, {"name": "المنتجات المميزة", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/app/v1/products/featured", "host": ["{{base_url}}"], "path": ["api", "app", "v1", "products", "featured"]}, "description": "جلب المنتجات المميزة (أحدث 10 منتجات)"}, "response": []}], "description": "APIs خاصة بالمنتجات"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://makkah-gold-jewelry.test", "type": "string"}]}