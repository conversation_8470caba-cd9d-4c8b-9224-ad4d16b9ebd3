// ثوابت التطبيق - Makkah Gold App Constants

// API Configuration
export const API_CONFIG = {
  BASE_URL: 'http://makkah-gold-jewelry.test/api/app/v1',
  TIMEOUT: 10000,
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
};

// Colors - الألوان
export const COLORS = {
  // الألوان الأساسية
  primary: '#FFD700',        // ذهبي أساسي
  primaryDark: '#B8860B',    // ذهبي داكن
  secondary: '#F5F5DC',      // بيج فاتح
  accent: '#DAA520',         // ذهبي متوسط
  
  // الألوان المحايدة
  white: '#FFFFFF',
  black: '#000000',
  gray: '#808080',
  lightGray: '#F5F5F5',
  darkGray: '#333333',
  
  // ألوان الحالة
  success: '#4CAF50',
  error: '#F44336',
  warning: '#FF9800',
  info: '#2196F3',
  
  // ألوان النص
  textPrimary: '#212121',
  textSecondary: '#757575',
  textLight: '#FFFFFF',
  
  // ألوان الخلفية
  background: '#FAFAFA',
  surface: '#FFFFFF',
  card: '#FFFFFF',
};

// Typography - الخطوط
export const FONTS = {
  // أحجام الخطوط
  sizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  
  // أوزان الخطوط
  weights: {
    light: '300' as const,
    regular: '400' as const,
    medium: '500' as const,
    semiBold: '600' as const,
    bold: '700' as const,
  },
};

// Spacing - المسافات
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

// Metal Types - أنواع المعادن
export const METAL_TYPES = {
  GOLD: 'gold',
  SILVER: 'silver',
  PLATINUM: 'platinum',
} as const;

// Metal Purities - عيارات المعادن
export const METAL_PURITIES = {
  GOLD: ['24K', '22K', '21K', '18K', '14K', '12K', '9K'],
  SILVER: ['999', '925', '900', '800', '600'],
  PLATINUM: ['950'],
} as const;

// Zakat Constants - ثوابت الزكاة
export const ZAKAT = {
  GOLD_NISAB_GRAMS: 85,      // نصاب الذهب بالجرام
  SILVER_NISAB_GRAMS: 595,   // نصاب الفضة بالجرام
  RATE: 0.025,               // معدل الزكاة 2.5%
  RATE_PERCENTAGE: '2.5%',   // معدل الزكاة كنص
} as const;

// Navigation Routes - مسارات التنقل
export const ROUTES = {
  // Bottom Tabs
  HOME: 'Home',
  PRICES: 'Prices',
  CALCULATORS: 'Calculators',
  PRODUCTS: 'Products',
  MORE: 'More',
  
  // Stack Screens
  PRICE_HISTORY: 'PriceHistory',
  ZAKAT_CALCULATOR: 'ZakatCalculator',
  JEWELRY_CALCULATOR: 'JewelryCalculator',
  PRODUCT_DETAILS: 'ProductDetails',
  CATEGORY_PRODUCTS: 'CategoryProducts',
  SETTINGS: 'Settings',
  ABOUT: 'About',
} as const;

// API Endpoints - نقاط النهاية للـ API
export const ENDPOINTS = {
  // Metal Prices
  CURRENT_PRICES: '/metal-prices/current',
  METAL_PRICES: (metalType: string) => `/metal-prices/${metalType}`,
  PRICE_HISTORY: (metalType: string, purity: string) => 
    `/metal-prices/${metalType}/${purity}/history`,
  CALCULATE_JEWELRY_VALUE: '/metal-prices/calculate-jewelry-value',
  
  // Zakat Calculator
  ZAKAT_GOLD: '/zakat/gold',
  ZAKAT_SILVER: '/zakat/silver',
  NISAB_INFO: '/zakat/nisab-info',
  
  // Categories & Products
  CATEGORIES: '/categories',
  CATEGORY_PRODUCTS: (id: number) => `/categories/${id}/products`,
  PRODUCTS: '/products',
  PRODUCT_DETAILS: (id: number) => `/products/${id}`,
  FEATURED_PRODUCTS: '/products/featured',
} as const;

// Error Messages - رسائل الخطأ
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'خطأ في الاتصال بالشبكة',
  SERVER_ERROR: 'خطأ في الخادم',
  UNKNOWN_ERROR: 'حدث خطأ غير متوقع',
  NO_DATA: 'لا توجد بيانات متاحة',
  LOADING_ERROR: 'خطأ في تحميل البيانات',
} as const;
