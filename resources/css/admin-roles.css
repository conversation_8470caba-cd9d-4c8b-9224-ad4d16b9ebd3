/* تحسينات تصميم صفحات الأدوار والصلاحيات */

/* تحسين عرض الصلاحيات في النموذج */
.fi-fo-checkbox-list .fi-fo-checkbox-list-option {
    @apply bg-white border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors;
}

.fi-fo-checkbox-list .fi-fo-checkbox-list-option:hover {
    @apply border-primary-300 shadow-sm;
}

.fi-fo-checkbox-list .fi-fo-checkbox-list-option input:checked + label {
    @apply text-primary-600 font-medium;
}

/* تحسين عرض الأقسام */
.fi-section {
    @apply bg-white border border-gray-200 rounded-xl shadow-sm;
}

.fi-section-header {
    @apply bg-gradient-to-r from-primary-50 to-primary-100 border-b border-gray-200 rounded-t-xl;
}

.fi-section-header-heading {
    @apply text-primary-900 font-semibold;
}

.fi-section-description {
    @apply text-gray-600 text-sm;
}

/* تحسين عرض البطاقات */
.fi-badge {
    @apply font-medium px-3 py-1 rounded-full text-xs;
}

/* تحسين عرض الجداول */
.fi-ta-table {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

.fi-ta-header-cell {
    @apply bg-gray-50 font-semibold text-gray-900 border-b border-gray-200;
}

.fi-ta-cell {
    @apply border-b border-gray-100;
}

.fi-ta-row:hover .fi-ta-cell {
    @apply bg-gray-50;
}

/* تحسين الأزرار */
.fi-btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 shadow-sm;
}

.fi-btn-secondary {
    @apply bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-700;
}

.fi-btn-danger {
    @apply bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800;
}

/* تحسين النماذج */
.fi-input {
    @apply border-gray-300 focus:border-primary-500 focus:ring-primary-500 rounded-lg;
}

.fi-select {
    @apply border-gray-300 focus:border-primary-500 focus:ring-primary-500 rounded-lg;
}

/* تحسين الرسائل التوضيحية */
.fi-fo-field-wrp-helper-text {
    @apply text-gray-500 text-sm mt-1;
}

/* تحسين عرض الأيقونات */
.fi-section-header-icon {
    @apply text-primary-600;
}

/* تحسين المساحات */
.fi-section-content {
    @apply p-6;
}

.fi-fo-field-wrp {
    @apply mb-4;
}

/* تحسين عرض الحالة الفارغة */
.fi-ta-empty-state {
    @apply py-12 text-center;
}

.fi-ta-empty-state-icon {
    @apply text-gray-400 mx-auto mb-4;
}

.fi-ta-empty-state-heading {
    @apply text-gray-900 font-semibold text-lg mb-2;
}

.fi-ta-empty-state-description {
    @apply text-gray-500 text-sm;
}

/* تحسين عرض الفلاتر */
.fi-ta-filters {
    @apply bg-gray-50 border-b border-gray-200 p-4 rounded-t-lg;
}

/* تحسين عرض الإجراءات */
.fi-ta-actions {
    @apply space-x-2;
}

/* تحسين عرض الصفحات */
.fi-ta-pagination {
    @apply bg-gray-50 border-t border-gray-200 p-4 rounded-b-lg;
}

/* تحسين عرض النصوص */
.fi-ta-text-item-label {
    @apply font-medium text-gray-900;
}

.fi-ta-text-item-description {
    @apply text-gray-500 text-sm;
}

/* تحسين عرض الشارات */
.fi-badge-success {
    @apply bg-green-100 text-green-800 border border-green-200;
}

.fi-badge-warning {
    @apply bg-yellow-100 text-yellow-800 border border-yellow-200;
}

.fi-badge-info {
    @apply bg-blue-100 text-blue-800 border border-blue-200;
}

.fi-badge-primary {
    @apply bg-primary-100 text-primary-800 border border-primary-200;
}

/* تحسين عرض النماذج المتقدمة */
.fi-fo-tabs {
    @apply border-b border-gray-200;
}

.fi-fo-tabs-tab {
    @apply px-4 py-2 font-medium text-gray-600 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-300;
}

.fi-fo-tabs-tab-active {
    @apply text-primary-600 border-primary-600;
}

/* تحسين الاستجابة للجوال */
@media (max-width: 768px) {
    .fi-section-content {
        @apply p-4;
    }

    .fi-fo-checkbox-list {
        @apply grid-cols-1;
    }

    .fi-ta-table {
        @apply text-sm;
    }
}

/* تحسين الطباعة */
@media print {
    .fi-btn,
    .fi-ta-actions {
        @apply hidden;
    }

    .fi-section {
        @apply shadow-none border border-gray-300;
    }
}

/* تحسينات خاصة بالأقسام الجديدة */

/* تحسين أقسام الصلاحيات */
.fi-section {
    @apply bg-white border border-gray-200 rounded-xl shadow-sm mb-6 transition-all duration-200;
}

.fi-section:hover {
    @apply shadow-md border-gray-300;
}

.fi-section-header {
    @apply bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200 rounded-t-xl p-4;
}

.fi-section-header-heading {
    @apply text-gray-900 font-semibold text-lg flex items-center;
}

.fi-section-header-heading svg {
    @apply w-5 h-5 ml-2 text-primary-600;
}

.fi-section-description {
    @apply text-gray-600 text-sm mt-1;
}

/* تحسين أزرار التحكم السريع */
.fi-ac-group-item {
    @apply bg-gradient-to-r from-primary-50 to-primary-100 hover:from-primary-100 hover:to-primary-200 border border-primary-200 rounded-lg shadow-sm transition-all duration-200 p-4;
}

.fi-ac-group-item:hover {
    @apply shadow-md transform scale-105 border-primary-300;
}

/* تحسين أقسام الصلاحيات */
.fi-section-header-actions {
    @apply space-x-2 flex items-center;
}

.fi-section-header-actions .fi-btn {
    @apply text-xs px-3 py-1 rounded-full font-medium;
}

/* تحسين قسم التحكم العام */
.fi-section:first-child {
    @apply bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200;
}

.fi-section:first-child .fi-section-header {
    @apply bg-gradient-to-r from-blue-100 to-indigo-100 border-blue-200;
}

.fi-section:first-child .fi-section-header-heading {
    @apply text-blue-900;
}

.fi-section:first-child .fi-section-description {
    @apply text-blue-700;
}

/* تحسين عرض الصلاحيات */
.fi-fo-checkbox-list-option {
    @apply bg-white border border-gray-200 rounded-lg p-3 hover:bg-primary-50 hover:border-primary-300 transition-all duration-200;
}

.fi-fo-checkbox-list-option:has(input:checked) {
    @apply bg-primary-50 border-primary-300 shadow-sm;
}

.fi-fo-checkbox-list-option label {
    @apply cursor-pointer text-sm font-medium text-gray-700;
}

.fi-fo-checkbox-list-option:has(input:checked) label {
    @apply text-primary-700;
}

/* تحسين المحتوى داخل الأقسام */
.fi-section-content {
    @apply p-6;
}

/* تحسين أقسام مختلفة بألوان مميزة */
.fi-section[data-section="users_roles"] {
    @apply border-blue-200;
}

.fi-section[data-section="users_roles"] .fi-section-header {
    @apply bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200;
}

.fi-section[data-section="products_categories"] {
    @apply border-green-200;
}

.fi-section[data-section="products_categories"] .fi-section-header {
    @apply bg-gradient-to-r from-green-50 to-green-100 border-green-200;
}

.fi-section[data-section="orders_sales"] {
    @apply border-orange-200;
}

.fi-section[data-section="orders_sales"] .fi-section-header {
    @apply bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200;
}

.fi-section[data-section="system_settings"] {
    @apply border-red-200;
}

.fi-section[data-section="system_settings"] .fi-section-header {
    @apply bg-gradient-to-r from-red-50 to-red-100 border-red-200;
}

/* تحسين عرض الشبكة */
.fi-fo-checkbox-list {
    @apply gap-3;
}

/* تحسين الاستجابة للتبويبات */
@media (max-width: 768px) {
    .fi-fo-tabs-tab {
        @apply px-3 py-2 text-sm;
    }

    .fi-fo-tabs-tab svg {
        @apply w-3 h-3;
    }

    .fi-fo-tabs-content {
        @apply p-4;
    }

    .fi-fo-checkbox-list {
        @apply grid-cols-1;
    }
}
