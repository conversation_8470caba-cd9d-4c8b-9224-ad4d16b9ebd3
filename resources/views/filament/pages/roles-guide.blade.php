<x-filament-panels::page>
    <div class="space-y-6">
        <!-- مقدمة -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
            <div class="flex items-center mb-4">
                <div class="flex-shrink-0">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <div class="mr-4">
                    <h3 class="text-lg font-semibold text-blue-900">نظام الأدوار والصلاحيات</h3>
                    <p class="text-blue-700">دليل شامل لفهم كيفية عمل نظام الأدوار والصلاحيات في النظام</p>
                </div>
            </div>
            <div class="text-sm text-blue-800 space-y-2">
                <p>• يستخدم النظام مكتبة <strong>Spatie Permission</strong> لإدارة الأدوار والصلاحيات</p>
                <p>• كل مستخدم يمكن أن يكون له دور واحد أو أكثر</p>
                <p>• كل دور يحتوي على مجموعة من الصلاحيات</p>
                <p>• الصلاحيات تحدد ما يمكن للمستخدم فعله في النظام</p>
            </div>
        </div>

        <!-- الأدوار المتاحة -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                    <svg class="w-6 h-6 text-gray-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    الأدوار المتاحة في النظام
                </h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($this->getRolesData() as $roleKey => $role)
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div class="flex items-center mb-3">
                                <div class="w-3 h-3 rounded-full bg-{{ $role['color'] }}-500 ml-2"></div>
                                <h3 class="text-lg font-semibold text-gray-900">{{ $role['name'] }}</h3>
                                <span class="mr-auto text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">{{ $roleKey }}</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">{{ $role['description'] }}</p>
                            <div class="space-y-2">
                                <h4 class="text-sm font-medium text-gray-900">الصلاحيات الرئيسية:</h4>
                                <ul class="text-xs text-gray-600 space-y-1">
                                    @foreach($role['permissions'] as $permission)
                                        <li class="flex items-center">
                                            <svg class="w-3 h-3 text-green-500 ml-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                            {{ $permission }}
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- فئات الصلاحيات -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                    <svg class="w-6 h-6 text-gray-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                    </svg>
                    فئات الصلاحيات
                </h2>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    @foreach($this->getPermissionCategories() as $category => $permissions)
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                                <div class="w-2 h-2 rounded-full bg-blue-500 ml-2"></div>
                                {{ $category }}
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                @foreach($permissions as $permissionKey => $permissionName)
                                    <div class="flex items-center justify-between bg-gray-50 rounded p-3">
                                        <span class="text-sm font-medium text-gray-900">{{ $permissionName }}</span>
                                        <code class="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded">{{ $permissionKey }}</code>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- نصائح وإرشادات -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div class="flex items-center mb-4">
                <svg class="w-6 h-6 text-yellow-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <h3 class="text-lg font-semibold text-yellow-900">نصائح مهمة</h3>
            </div>
            <div class="text-sm text-yellow-800 space-y-2">
                <p>• <strong>لا تحذف دور السوبر أدمن:</strong> هذا الدور محمي ولا يمكن حذفه لأسباب أمنية</p>
                <p>• <strong>كن حذراً عند تعديل الصلاحيات:</strong> تأكد من أن المستخدمين لديهم الصلاحيات المناسبة لأدوارهم</p>
                <p>• <strong>استخدم مبدأ الحد الأدنى من الصلاحيات:</strong> امنح المستخدمين أقل صلاحيات ممكنة لأداء مهامهم</p>
                <p>• <strong>راجع الصلاحيات بانتظام:</strong> تأكد من أن الصلاحيات ما زالت مناسبة لاحتياجات المستخدمين</p>
                <p>• <strong>اختبر الصلاحيات:</strong> تأكد من أن الصلاحيات تعمل كما هو متوقع قبل تطبيقها على المستخدمين</p>
            </div>
        </div>
    </div>
</x-filament-panels::page>
