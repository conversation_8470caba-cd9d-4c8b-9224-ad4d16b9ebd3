<div>


    <!-- Hero Section -->
    <section class="relative">
        <!-- <PERSON> Slider -->
        <div class="swiper hero-slider">
            <div class="swiper-wrapper">
                @if (count($sliders) > 0)
                    @foreach ($sliders as $slider)
                        <div class="swiper-slide">
                            <div class="relative h-[500px] md:h-[600px]">
                                <img src="{{ asset('storage/' . $slider->image) }}"
                                    alt="{{ $locale == 'ar' ? $slider->title_ar : $slider->title_en }}"
                                    class="absolute inset-0 w-full h-full object-cover">
                                <div class="absolute inset-0 bg-black bg-opacity-40"></div>
                                <div class="absolute inset-0 flex items-center">
                                    <div class="container mx-auto px-4">
                                        <div class="max-w-xl">
                                            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
                                                {{ $locale == 'ar' ? $slider->title_ar : $slider->title_en }}</h1>
                                            <p class="text-xl text-white/90 mb-8">
                                                {{ $locale == 'ar' ? $slider->description_ar : $slider->description_en }}
                                            </p>
                                            <div class="flex flex-wrap gap-4">
                                                <a href="{{ $slider->button_link }}"
                                                    class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                                                    {{ $locale == 'ar' ? $slider->button_text_ar : $slider->button_text_en }}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="swiper-slide">
                        <div class="relative h-[500px] md:h-[600px]">
                            <img src="{{ asset('images/hero/hero-1.jpg') }}" alt="{{ __('Makkah Gold') }}"
                                class="absolute inset-0 w-full h-full object-cover">
                            <div class="absolute inset-0 bg-black bg-opacity-40"></div>
                            <div class="absolute inset-0 flex items-center">
                                <div class="container mx-auto px-4">
                                    <div class="max-w-xl">
                                        <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
                                            {{ __('Luxury Jewelry with Unique Designs') }}</h1>
                                        <p class="text-xl text-white/90 mb-8">
                                            {{ __('Discover our exclusive collection of gold, silver and platinum jewelry') }}
                                        </p>
                                        <div class="flex flex-wrap gap-4">
                                            <a href="{{ route('products') }}"
                                                class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                                                @if (isset($displayOnlyMode) && $displayOnlyMode)
                                                    {{ __('Browse Products') }}
                                                @else
                                                    {{ __('Shop Now') }}
                                                @endif
                                            </a>
                                            <a href="{{ route('appointment') }}"
                                                class="bg-white hover:bg-gray-100 text-primary-500 px-6 py-3 rounded-full font-medium transition duration-300">{{ __('Book Appointment') }}</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
            <!-- Add Pagination -->
            <div class="swiper-pagination"></div>
            <!-- Add Navigation -->
            <div class="swiper-button-next text-white"></div>
            <div class="swiper-button-prev text-white"></div>
        </div>
    </section>

    <!-- Features Section -->
    @if (isset($showFeatures) && $showFeatures && isset($this->features) && count($this->features) > 0)
        <section class="py-12 bg-white">
            <div class="container mx-auto px-4">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-8">
                    @foreach ($this->features as $feature)
                        <div
                            class="flex flex-col items-center text-center p-6 rounded-lg hover:shadow-lg transition duration-300">
                            <div class="text-primary-500 mb-4">
                                <i class="{{ $feature->icon ?? 'fas fa-gem' }} text-4xl"></i>
                            </div>
                            <h3 class="text-xl font-bold mb-2">
                                {{ $locale == 'ar' ? $feature->title_ar : $feature->title_en }}</h3>
                            <p class="text-gray-600">
                                {{ $locale == 'ar' ? $feature->description_ar : $feature->description_en }}</p>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <!-- Categories Section -->
    @if (count($categories) > 0)
        <section class="py-12 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">{{ __('Shop by Category') }}</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">
                        {{ __('Discover our diverse collection of jewelry categorized to make your shopping experience easier') }}
                    </p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8">
                    @foreach ($categories as $category)
                        <a href="{{ route('category', $category->slug) }}" class="group">
                            <div class="relative h-80 rounded-lg overflow-hidden">
                                <img src="{{ asset('storage/' . $category->image) }}" alt="{{ $category->name }}"
                                    class="w-full h-full object-cover transition duration-500 group-hover:scale-110">
                                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                                <div class="absolute bottom-0 left-0 right-0 p-6">
                                    <h3 class="text-2xl font-bold text-white mb-2">{{ $category->name }}</h3>
                                    <p class="text-white/80 mb-4">{{ $category->products_count ?? 0 }}
                                        {{ __('products') }}</p>
                                    <span
                                        class="inline-block bg-white text-primary-500 px-4 py-2 rounded-full font-medium transition duration-300 group-hover:bg-primary-500 group-hover:text-white">
                                        @if (isset($displayOnlyMode) && $displayOnlyMode)
                                            {{ __('Browse Products') }}
                                        @else
                                            {{ __('Shop Now') }}
                                        @endif
                                    </span>
                                </div>
                            </div>
                        </a>
                    @endforeach
                </div>

                <div class="text-center mt-10">
                    <a href="{{ route('products') }}"
                        class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">{{ __('View All Categories') }}</a>
                </div>
            </div>
        </section>
    @endif

    <!-- Featured Products Section -->
    @if (count($featuredProducts) > 0)
        <section class="py-12 bg-white">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">{{ __('Featured Products') }}</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">
                        {{ __('Discover our featured collection of luxury jewelry carefully selected to suit your refined taste') }}
                    </p>
                </div>

                <div wire:loading.delay class="w-full text-center py-4">
                    <div
                        class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500">
                    </div>
                    <p class="mt-2 text-gray-500">{{ __('Loading products...') }}</p>
                </div>

                <div wire:loading.delay.remove
                    class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-8 product-grid">
                    @foreach ($featuredProducts as $product)
                        <div
                            class="product-card bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition duration-300">
                            <a href="{{ route('product.show', $product->slug) }}" class="block relative">
                                <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}"
                                    class="w-full h-40 sm:h-64 object-cover product-image-container">
                                @if ($product->discount_percentage > 0)
                                    <span
                                        class="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded">{{ __('Discount') }}
                                        {{ $product->discount_percentage }}%</span>
                                @endif
                            </a>
                            <div class="p-2 sm:p-4">
                                <a href="{{ route('category', $product->category->slug) }}"
                                    class="text-xs text-primary-500 hover:text-primary-600 mb-2 inline-block">{{ $product->category->name }}</a>
                                <h3 class="text-sm sm:text-lg font-bold mb-2">
                                    <a href="{{ route('product.show', $product->slug) }}"
                                        class="text-gray-800 hover:text-primary-500">{{ $product->name }}</a>
                                </h3>
                                @if (isset($showRatings) && $showRatings)
                                    <div class="flex items-center mb-3">
                                        <div class="flex text-yellow-400">
                                            @for ($i = 1; $i <= 5; $i++)
                                                @if ($i <= $product->rating)
                                                    <i class="fas fa-star"></i>
                                                @elseif($i - 0.5 <= $product->rating)
                                                    <i class="fas fa-star-half-alt"></i>
                                                @else
                                                    <i class="far fa-star"></i>
                                                @endif
                                            @endfor
                                        </div>
                                        <span
                                            class="text-gray-500 text-xs mr-1">({{ $product->reviews_count ?? 0 }})</span>
                                    </div>
                                @endif
                                <!-- استخدام Shopping Controls Component -->
                                <x-shopping-controls :product="$product" :show-wishlist="$showWishlist" :whatsapp-phone="$whatsappPhone" />
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="text-center mt-10">
                    <a href="{{ route('products') }}"
                        class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">{{ __('View All Products') }}</a>
                </div>
            </div>
        </section>
    @endif

    <!-- Gold Prices Section -->
    @if (count($goldPrices) > 0)
        <section class="py-12 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">{{ __('Today\'s Gold Prices') }}</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">
                        {{ __('Follow the latest gold prices in Egypt in Egyptian pounds') }}</p>
                </div>

                <div wire:loading.delay class="w-full text-center py-4">
                    <div
                        class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500">
                    </div>
                    <p class="mt-2 text-gray-500">{{ __('Loading prices...') }}</p>
                </div>

                <div wire:loading.delay.remove
                    class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3 md:gap-6">
                    @foreach ($goldPrices as $price)
                        <div
                            class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition duration-300">
                            <div class="gold-gradient p-4 text-center">
                                <h3 class="text-xl font-bold text-gray-800">{{ __('Gold') }} {{ $price->purity }}
                                    {{ __('Karat') }}</h3>
                            </div>
                            <div class="p-6 text-center">
                                <div class="text-3xl font-bold text-gray-800 mb-2">
                                    {{ number_format($price->price_per_gram, 2) }}</div>
                                <p class="text-gray-600">{{ __('Egyptian Pound / Gram') }}</p>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="text-center mt-10">
                    <a href="{{ route('metal-prices') }}"
                        class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">{{ __('View All Prices') }}</a>
                </div>
            </div>
        </section>
    @endif

    <!-- New Arrivals Section -->
    @if (count($newArrivals) > 0)
        <section class="py-12 bg-white">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">{{ __('New Arrivals') }}</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">
                        {{ __('Discover the latest additions to our luxury jewelry collection') }}</p>
                </div>

                <div wire:loading.delay class="w-full text-center py-4">
                    <div
                        class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500">
                    </div>
                    <p class="mt-2 text-gray-500">{{ __('Loading products...') }}</p>
                </div>

                <div wire:loading.delay.remove
                    class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-8 product-grid">
                    @foreach ($newArrivals as $product)
                        <div
                            class="product-card bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition duration-300">
                            <a href="{{ route('product.show', $product->slug) }}" class="block relative">
                                <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}"
                                    class="w-full h-40 sm:h-64 object-cover product-image-container">
                                <span
                                    class="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded">{{ __('New') }}</span>
                            </a>
                            <div class="p-2 sm:p-4">
                                <a href="{{ route('category', $product->category->slug) }}"
                                    class="text-xs text-primary-500 hover:text-primary-600 mb-2 inline-block">{{ $product->category->name }}</a>
                                <h3 class="text-sm sm:text-lg font-bold mb-2">
                                    <a href="{{ route('product.show', $product->slug) }}"
                                        class="text-gray-800 hover:text-primary-500">{{ $product->name }}</a>
                                </h3>
                                @if (isset($showRatings) && $showRatings)
                                    <div class="flex items-center mb-3">
                                        <div class="flex text-yellow-400">
                                            @for ($i = 1; $i <= 5; $i++)
                                                @if ($i <= $product->rating)
                                                    <i class="fas fa-star"></i>
                                                @elseif($i - 0.5 <= $product->rating)
                                                    <i class="fas fa-star-half-alt"></i>
                                                @else
                                                    <i class="far fa-star"></i>
                                                @endif
                                            @endfor
                                        </div>
                                        <span
                                            class="text-gray-500 text-xs mr-1">({{ $product->reviews_count ?? 0 }})</span>
                                    </div>
                                @endif
                                <!-- استخدام Shopping Controls Component -->
                                <x-shopping-controls :product="$product" :show-wishlist="$showWishlist" :whatsapp-phone="$whatsappPhone" />
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="text-center mt-10">
                    <a href="{{ route('products') }}"
                        class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">{{ __('View All New Products') }}</a>
                </div>
            </div>
        </section>
    @endif

    <!-- Testimonials Section -->
    @if (isset($showTestimonials) && $showTestimonials && isset($this->testimonials) && count($this->testimonials) > 0)
        <section class="py-12 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">{{ __('Customer Reviews') }}</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">
                        {{ __('Read what our customers say about their experience with Makkah Gold') }}</p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-8">
                    @foreach ($this->testimonials as $testimonial)
                        <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                            @if (isset($showRatings) && $showRatings)
                                <div class="flex text-yellow-400 mb-4">
                                    @for ($i = 1; $i <= 5; $i++)
                                        @if ($i <= $testimonial->rating)
                                            <i class="fas fa-star"></i>
                                        @elseif($i - 0.5 <= $testimonial->rating)
                                            <i class="fas fa-star-half-alt"></i>
                                        @else
                                            <i class="far fa-star"></i>
                                        @endif
                                    @endfor
                                </div>
                            @endif
                            <p class="text-gray-600 mb-6">
                                {{ $locale == 'ar' ? $testimonial->content_ar : $testimonial->content_en }}</p>
                            <div class="flex items-center">
                                @if ($testimonial->image)
                                    <img src="{{ asset('storage/' . $testimonial->image) }}"
                                        alt="{{ $locale == 'ar' ? $testimonial->client_name_ar : $testimonial->client_name_en }}"
                                        class="w-12 h-12 rounded-full object-cover ml-4">
                                @else
                                    <div
                                        class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center ml-4">
                                        <i class="fas fa-user text-primary-500"></i>
                                    </div>
                                @endif
                                <div>
                                    <h4 class="font-bold text-gray-800">
                                        {{ $locale == 'ar' ? $testimonial->client_name_ar : $testimonial->client_name_en }}
                                    </h4>
                                    @if ($testimonial->location_ar || $testimonial->location_en)
                                        <p class="text-gray-500 text-sm">
                                            {{ $locale == 'ar' ? $testimonial->location_ar : $testimonial->location_en }}
                                        </p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif



    <!-- Instagram Feed Section -->
    {{-- <section class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4">{{ __('Follow Us on Instagram') }}</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">{{ __('See our latest designs and products on our Instagram account') }}</p>
            </div>

            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2 sm:gap-4">
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="block relative group overflow-hidden">
                    <img src="{{ asset('images/instagram/post-1.jpg') }}" alt="Instagram Post"
                        class="w-full aspect-square object-cover transition duration-500 group-hover:scale-110">
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center transition duration-300 opacity-0 group-hover:opacity-100">
                        <i class="fab fa-instagram text-white text-2xl"></i>
                    </div>
                </a>
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="block relative group overflow-hidden">
                    <img src="{{ asset('images/instagram/post-2.jpg') }}" alt="Instagram Post"
                        class="w-full aspect-square object-cover transition duration-500 group-hover:scale-110">
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center transition duration-300 opacity-0 group-hover:opacity-100">
                        <i class="fab fa-instagram text-white text-2xl"></i>
                    </div>
                </a>
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="block relative group overflow-hidden">
                    <img src="{{ asset('images/instagram/post-3.jpg') }}" alt="Instagram Post"
                        class="w-full aspect-square object-cover transition duration-500 group-hover:scale-110">
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center transition duration-300 opacity-0 group-hover:opacity-100">
                        <i class="fab fa-instagram text-white text-2xl"></i>
                    </div>
                </a>
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="block relative group overflow-hidden">
                    <img src="{{ asset('images/instagram/post-4.jpg') }}" alt="Instagram Post"
                        class="w-full aspect-square object-cover transition duration-500 group-hover:scale-110">
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center transition duration-300 opacity-0 group-hover:opacity-100">
                        <i class="fab fa-instagram text-white text-2xl"></i>
                    </div>
                </a>
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="block relative group overflow-hidden">
                    <img src="{{ asset('images/instagram/post-5.jpg') }}" alt="Instagram Post"
                        class="w-full aspect-square object-cover transition duration-500 group-hover:scale-110">
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center transition duration-300 opacity-0 group-hover:opacity-100">
                        <i class="fab fa-instagram text-white text-2xl"></i>
                    </div>
                </a>
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="block relative group overflow-hidden">
                    <img src="{{ asset('images/instagram/post-6.jpg') }}" alt="Instagram Post"
                        class="w-full aspect-square object-cover transition duration-500 group-hover:scale-110">
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center transition duration-300 opacity-0 group-hover:opacity-100">
                        <i class="fab fa-instagram text-white text-2xl"></i>
                    </div>
                </a>
            </div>

            <div class="text-center mt-8">
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="inline-flex items-center text-primary-500 hover:text-primary-600 font-medium">
                    <span>@makkahgold</span>
                    <i class="fas fa-arrow-left mr-2"></i>
                </a>
            </div>
        </div>
    </section> --}}

    <!-- Initialize Swiper -->
    <script>
        document.addEventListener('livewire:initialized', function() {
            const heroSlider = new Swiper('.hero-slider', {
                // Optional parameters
                loop: true,
                effect: 'fade',
                fadeEffect: {
                    crossFade: true
                },
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },

                // If we need pagination
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },

                // Navigation arrows
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
            });
        });
    </script>
</div>
