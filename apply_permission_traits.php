<?php

echo "🔧 تطبيق traits الصلاحيات على جميع ملفات Filament\n";
echo "===============================================\n\n";

// قائمة الموارد التي تحتاج للـ trait
$resources = [
    'app/Filament/Resources/AppointmentResource.php',
    'app/Filament/Resources/BlogPostResource.php',
    'app/Filament/Resources/CategoryResource.php',
    'app/Filament/Resources/FeatureResource.php',
    'app/Filament/Resources/HomeSliderResource.php',
    'app/Filament/Resources/JobApplicationResource.php',
    'app/Filament/Resources/JobResource.php',
    'app/Filament/Resources/LanguageManagerResource.php',
    'app/Filament/Resources/LanguageResource.php',
    'app/Filament/Resources/MetalPriceResource.php',
    'app/Filament/Resources/MetalPurityResource.php',
    'app/Filament/Resources/MetalTypeResource.php',
    'app/Filament/Resources/NewsletterResource.php',
    'app/Filament/Resources/OrderResource.php',
    'app/Filament/Resources/PageResource.php',
    'app/Filament/Resources/PermissionResource.php',
    'app/Filament/Resources/ProductResource.php',
    'app/Filament/Resources/SettingChangeResource.php',
    'app/Filament/Resources/SiteSettingResource.php',
    'app/Filament/Resources/StoreResource.php',
    'app/Filament/Resources/SuperAdminSettingResource.php',
    'app/Filament/Resources/TestimonialResource.php',
];

// قائمة الصفحات التي تحتاج للـ trait
$pages = [
    'app/Filament/Pages/PreviewSettings.php',
    'app/Filament/Pages/SearchSettings.php',
    'app/Filament/Pages/SiteSettingsManager.php',
    'app/Filament/Pages/TranslationsManager.php',
];

// قائمة الودجات التي تحتاج للـ trait
$widgets = [
    'app/Filament/Widgets/GoldPriceChart.php',
    'app/Filament/Widgets/LatestOrders.php',
    'app/Filament/Widgets/MaintenanceModeToggle.php',
    'app/Filament/Widgets/SalesChart.php',
    'app/Filament/Widgets/UpcomingAppointments.php',
];

/**
 * إضافة trait للموارد
 */
function addTraitToResource($filePath) {
    if (!file_exists($filePath)) {
        echo "   ❌ الملف غير موجود: {$filePath}\n";
        return false;
    }
    
    $content = file_get_contents($filePath);
    
    // التحقق من وجود الـ trait مسبقاً
    if (strpos($content, 'use HasPermissionFiltering;') !== false) {
        echo "   ✅ الـ trait موجود مسبقاً في: " . basename($filePath) . "\n";
        return true;
    }
    
    // إضافة الـ import
    if (strpos($content, 'use App\Traits\HasPermissionFiltering;') === false) {
        $content = str_replace(
            'use Illuminate\Database\Eloquent\SoftDeletingScope;',
            "use Illuminate\Database\Eloquent\SoftDeletingScope;\nuse App\Traits\HasPermissionFiltering;",
            $content
        );
        
        // إذا لم يجد SoftDeletingScope، ابحث عن مكان آخر
        if (strpos($content, 'use App\Traits\HasPermissionFiltering;') === false) {
            $content = preg_replace(
                '/(use [^;]+;)(\n\nclass)/m',
                "$1\nuse App\Traits\HasPermissionFiltering;$2",
                $content
            );
        }
    }
    
    // إضافة الـ trait داخل الكلاس
    $content = preg_replace(
        '/(class \w+ extends Resource\s*\{)(\s*)/m',
        "$1\n    use HasPermissionFiltering;\n$2",
        $content
    );
    
    file_put_contents($filePath, $content);
    echo "   ✅ تم إضافة الـ trait إلى: " . basename($filePath) . "\n";
    return true;
}

/**
 * إضافة trait للصفحات
 */
function addTraitToPage($filePath) {
    if (!file_exists($filePath)) {
        echo "   ❌ الملف غير موجود: {$filePath}\n";
        return false;
    }
    
    $content = file_get_contents($filePath);
    
    // التحقق من وجود الـ trait مسبقاً
    if (strpos($content, 'use HasPagePermissionFiltering;') !== false) {
        echo "   ✅ الـ trait موجود مسبقاً في: " . basename($filePath) . "\n";
        return true;
    }
    
    // إضافة الـ import
    if (strpos($content, 'use App\Traits\HasPagePermissionFiltering;') === false) {
        $content = preg_replace(
            '/(use [^;]+;)(\n\nclass)/m',
            "$1\nuse App\Traits\HasPagePermissionFiltering;$2",
            $content
        );
    }
    
    // إضافة الـ trait داخل الكلاس
    $content = preg_replace(
        '/(class \w+ extends Page\s*\{)(\s*)/m',
        "$1\n    use HasPagePermissionFiltering;\n$2",
        $content
    );
    
    file_put_contents($filePath, $content);
    echo "   ✅ تم إضافة الـ trait إلى: " . basename($filePath) . "\n";
    return true;
}

/**
 * إضافة trait للودجات
 */
function addTraitToWidget($filePath) {
    if (!file_exists($filePath)) {
        echo "   ❌ الملف غير موجود: {$filePath}\n";
        return false;
    }
    
    $content = file_get_contents($filePath);
    
    // التحقق من وجود الـ trait مسبقاً
    if (strpos($content, 'use HasWidgetPermissionFiltering;') !== false) {
        echo "   ✅ الـ trait موجود مسبقاً في: " . basename($filePath) . "\n";
        return true;
    }
    
    // إضافة الـ import
    if (strpos($content, 'use App\Traits\HasWidgetPermissionFiltering;') === false) {
        $content = preg_replace(
            '/(use [^;]+;)(\n\nclass)/m',
            "$1\nuse App\Traits\HasWidgetPermissionFiltering;$2",
            $content
        );
    }
    
    // إضافة الـ trait داخل الكلاس
    $content = preg_replace(
        '/(class \w+ extends \w+\s*\{)(\s*)/m',
        "$1\n    use HasWidgetPermissionFiltering;\n$2",
        $content
    );
    
    file_put_contents($filePath, $content);
    echo "   ✅ تم إضافة الـ trait إلى: " . basename($filePath) . "\n";
    return true;
}

// تطبيق الـ traits على الموارد
echo "1️⃣ تطبيق HasPermissionFiltering على الموارد:\n";
echo "---------------------------------------------\n";

$resourcesProcessed = 0;
foreach ($resources as $resource) {
    if (addTraitToResource($resource)) {
        $resourcesProcessed++;
    }
}

echo "   📊 تم معالجة {$resourcesProcessed} من " . count($resources) . " مورد\n\n";

// تطبيق الـ traits على الصفحات
echo "2️⃣ تطبيق HasPagePermissionFiltering على الصفحات:\n";
echo "------------------------------------------------\n";

$pagesProcessed = 0;
foreach ($pages as $page) {
    if (addTraitToPage($page)) {
        $pagesProcessed++;
    }
}

echo "   📊 تم معالجة {$pagesProcessed} من " . count($pages) . " صفحة\n\n";

// تطبيق الـ traits على الودجات
echo "3️⃣ تطبيق HasWidgetPermissionFiltering على الودجات:\n";
echo "------------------------------------------------\n";

$widgetsProcessed = 0;
foreach ($widgets as $widget) {
    if (addTraitToWidget($widget)) {
        $widgetsProcessed++;
    }
}

echo "   📊 تم معالجة {$widgetsProcessed} من " . count($widgets) . " ودجة\n\n";

// ملخص النتائج
echo "📋 ملخص العملية:\n";
echo "================\n";
echo "✅ الموارد: {$resourcesProcessed}/" . count($resources) . " تم معالجتها\n";
echo "✅ الصفحات: {$pagesProcessed}/" . count($pages) . " تم معالجتها\n";
echo "✅ الودجات: {$widgetsProcessed}/" . count($widgets) . " تم معالجتها\n\n";

$totalProcessed = $resourcesProcessed + $pagesProcessed + $widgetsProcessed;
$totalFiles = count($resources) + count($pages) + count($widgets);

echo "🎯 إجمالي الملفات المعالجة: {$totalProcessed}/{$totalFiles}\n";

if ($totalProcessed == $totalFiles) {
    echo "🎉 تم تطبيق جميع الـ traits بنجاح!\n";
    echo "💡 التوصية: اختبر الواجهة الآن للتأكد من عمل الفلترة\n";
} else {
    echo "⚠️ بعض الملفات لم يتم معالجتها - راجع الأخطاء أعلاه\n";
}

echo "\n";
