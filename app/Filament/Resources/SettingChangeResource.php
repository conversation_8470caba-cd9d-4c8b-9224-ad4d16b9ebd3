<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SettingChangeResource\Pages;
use App\Models\SettingChange;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SettingChangeResource extends Resource
{
    protected static ?string $model = SettingChange::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel = 'سجل التغييرات';

    protected static ?string $modelLabel = 'تغيير الإعدادات';

    protected static ?string $pluralModelLabel = 'سجل تغييرات الإعدادات';

    protected static ?string $navigationGroup = 'إعدادات النظام المتقدمة';

    protected static ?int $navigationSort = 5;

    protected static bool $shouldRegisterNavigation = true;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('setting_key')
                    ->label('المفتاح')
                    ->required()
                    ->maxLength(255)
                    ->disabled(),
                Forms\Components\Textarea::make('old_value')
                    ->label('القيمة القديمة')
                    ->disabled(),
                Forms\Components\Textarea::make('new_value')
                    ->label('القيمة الجديدة')
                    ->disabled(),
                Forms\Components\TextInput::make('ip_address')
                    ->label('عنوان IP')
                    ->disabled(),
                Forms\Components\TextInput::make('user_agent')
                    ->label('متصفح المستخدم')
                    ->disabled(),
                Forms\Components\DateTimePicker::make('created_at')
                    ->label('تاريخ التغيير')
                    ->disabled(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('المستخدم')
                    ->searchable(),
                Tables\Columns\TextColumn::make('setting_key')
                    ->label('المفتاح')
                    ->searchable(),
                Tables\Columns\TextColumn::make('old_value')
                    ->label('القيمة القديمة')
                    ->limit(30),
                Tables\Columns\TextColumn::make('new_value')
                    ->label('القيمة الجديدة')
                    ->limit(30),
                Tables\Columns\TextColumn::make('ip_address')
                    ->label('عنوان IP')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ التغيير')
                    ->dateTime('d/m/Y H:i:s')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('من تاريخ'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('إلى تاريخ'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
                Tables\Filters\SelectFilter::make('setting_key')
                    ->label('المفتاح')
                    ->options(function () {
                        return SettingChange::distinct()->pluck('setting_key', 'setting_key')->toArray();
                    }),
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('المستخدم')
                    ->relationship('user', 'name'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // No bulk actions needed
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSettingChanges::route('/'),
            'view' => Pages\ViewSettingChange::route('/{record}'),
        ];
    }
}
