<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JobResource\Pages;
use App\Filament\Resources\JobResource\RelationManagers;
use App\Models\Job;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class JobResource extends Resource
{
    protected static ?string $model = Job::class;

    protected static ?string $navigationIcon = 'heroicon-o-briefcase';

    protected static ?string $navigationGroup = 'الخدمات';

    protected static ?int $navigationSort = 4;

    public static function getNavigationLabel(): string
    {
        return __('الوظائف');
    }

    public static function getModelLabel(): string
    {
        return __('وظيفة');
    }

    public static function getPluralModelLabel(): string
    {
        return __('الوظائف');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('المعلومات الأساسية')
                            ->schema([
                                Forms\Components\TextInput::make('title')
                                    ->label('العنوان')
                                    ->required()
                                    ->maxLength(255)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(fn (string $operation, $state, Forms\Set $set) =>
                                        $operation === 'create' ? $set('slug', \Illuminate\Support\Str::slug($state)) : null),

                                Forms\Components\TextInput::make('slug')
                                    ->label('المعرف الفريد (Slug)')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true),

                                Forms\Components\TextInput::make('location')
                                    ->label('الموقع')
                                    ->required()
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('type')
                                    ->label('نوع الوظيفة')
                                    ->required()
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('department')
                                    ->label('القسم')
                                    ->maxLength(255),
                            ]),

                        Forms\Components\Section::make('التفاصيل')
                            ->schema([
                                Forms\Components\RichEditor::make('description')
                                    ->label('الوصف')
                                    ->required()
                                    ->fileAttachmentsDisk('public')
                                    ->fileAttachmentsDirectory('jobs')
                                    ->toolbarButtons([
                                        'blockquote',
                                        'bold',
                                        'bulletList',
                                        'codeBlock',
                                        'h2',
                                        'h3',
                                        'italic',
                                        'link',
                                        'orderedList',
                                        'redo',
                                        'strike',
                                        'underline',
                                        'undo',
                                    ]),

                                Forms\Components\RichEditor::make('requirements')
                                    ->label('المتطلبات')
                                    ->required()
                                    ->fileAttachmentsDisk('public')
                                    ->fileAttachmentsDirectory('jobs')
                                    ->toolbarButtons([
                                        'blockquote',
                                        'bold',
                                        'bulletList',
                                        'codeBlock',
                                        'h2',
                                        'h3',
                                        'italic',
                                        'link',
                                        'orderedList',
                                        'redo',
                                        'strike',
                                        'underline',
                                        'undo',
                                    ]),

                                Forms\Components\RichEditor::make('responsibilities')
                                    ->label('المسؤوليات')
                                    ->fileAttachmentsDisk('public')
                                    ->fileAttachmentsDirectory('jobs')
                                    ->toolbarButtons([
                                        'blockquote',
                                        'bold',
                                        'bulletList',
                                        'codeBlock',
                                        'h2',
                                        'h3',
                                        'italic',
                                        'link',
                                        'orderedList',
                                        'redo',
                                        'strike',
                                        'underline',
                                        'undo',
                                    ]),
                            ]),

                        Forms\Components\Section::make('الترجمة الإنجليزية')
                            ->schema([
                                Forms\Components\TextInput::make('translations.en.title')
                                    ->label('العنوان (إنجليزي)')
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('translations.en.location')
                                    ->label('الموقع (إنجليزي)')
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('translations.en.type')
                                    ->label('نوع الوظيفة (إنجليزي)')
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('translations.en.department')
                                    ->label('القسم (إنجليزي)')
                                    ->maxLength(255),

                                Forms\Components\RichEditor::make('translations.en.description')
                                    ->label('الوصف (إنجليزي)')
                                    ->fileAttachmentsDisk('public')
                                    ->fileAttachmentsDirectory('jobs')
                                    ->toolbarButtons([
                                        'blockquote',
                                        'bold',
                                        'bulletList',
                                        'codeBlock',
                                        'h2',
                                        'h3',
                                        'italic',
                                        'link',
                                        'orderedList',
                                        'redo',
                                        'strike',
                                        'underline',
                                        'undo',
                                    ]),

                                Forms\Components\RichEditor::make('translations.en.requirements')
                                    ->label('المتطلبات (إنجليزي)')
                                    ->fileAttachmentsDisk('public')
                                    ->fileAttachmentsDirectory('jobs')
                                    ->toolbarButtons([
                                        'blockquote',
                                        'bold',
                                        'bulletList',
                                        'codeBlock',
                                        'h2',
                                        'h3',
                                        'italic',
                                        'link',
                                        'orderedList',
                                        'redo',
                                        'strike',
                                        'underline',
                                        'undo',
                                    ]),

                                Forms\Components\RichEditor::make('translations.en.responsibilities')
                                    ->label('المسؤوليات (إنجليزي)')
                                    ->fileAttachmentsDisk('public')
                                    ->fileAttachmentsDirectory('jobs')
                                    ->toolbarButtons([
                                        'blockquote',
                                        'bold',
                                        'bulletList',
                                        'codeBlock',
                                        'h2',
                                        'h3',
                                        'italic',
                                        'link',
                                        'orderedList',
                                        'redo',
                                        'strike',
                                        'underline',
                                        'undo',
                                    ]),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('الحالة')
                            ->schema([
                                Forms\Components\Toggle::make('is_featured')
                                    ->label('مميزة')
                                    ->default(false),

                                Forms\Components\Toggle::make('is_active')
                                    ->label('نشطة')
                                    ->default(true),

                                Forms\Components\DateTimePicker::make('expires_at')
                                    ->label('تاريخ انتهاء الوظيفة')
                                    ->nullable(),
                            ]),

                        Forms\Components\Section::make('الراتب')
                            ->schema([
                                Forms\Components\TextInput::make('salary_min')
                                    ->label('الحد الأدنى للراتب')
                                    ->numeric()
                                    ->nullable(),

                                Forms\Components\TextInput::make('salary_max')
                                    ->label('الحد الأقصى للراتب')
                                    ->numeric()
                                    ->nullable(),
                            ]),

                        Forms\Components\Section::make('الإحصائيات')
                            ->schema([
                                Forms\Components\Placeholder::make('applications_count')
                                    ->label('عدد المتقدمين')
                                    ->content(fn ($record) => $record ? $record->applications()->count() : 0),

                                Forms\Components\Placeholder::make('created_at')
                                    ->label('تاريخ الإنشاء')
                                    ->content(fn ($record) => $record ? $record->created_at->format('Y-m-d H:i') : '-'),

                                Forms\Components\Placeholder::make('updated_at')
                                    ->label('تاريخ التحديث')
                                    ->content(fn ($record) => $record ? $record->updated_at->format('Y-m-d H:i') : '-'),
                            ])
                            ->hidden(fn ($record) => $record === null),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('العنوان')
                    ->searchable()
                    ->sortable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('location')
                    ->label('الموقع')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->label('نوع الوظيفة')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('department')
                    ->label('القسم')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_featured')
                    ->label('مميزة')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشطة')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('expires_at')
                    ->label('تاريخ الانتهاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('نوع الوظيفة')
                    ->options(fn () => Job::distinct()->pluck('type', 'type')->toArray()),

                Tables\Filters\SelectFilter::make('department')
                    ->label('القسم')
                    ->options(fn () => Job::distinct()->pluck('department', 'department')->toArray()),

                Tables\Filters\TernaryFilter::make('is_featured')
                    ->label('مميزة'),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('نشطة'),

                Tables\Filters\Filter::make('expires_at')
                    ->label('غير منتهية')
                    ->query(fn (Builder $query) => $query->where(function ($query) {
                        $query->whereNull('expires_at')
                              ->orWhere('expires_at', '>=', now());
                    }))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('تفعيل')
                        ->action(fn (\Illuminate\Support\Collection $records) => $records->each->update(['is_active' => true]))
                        ->requiresConfirmation()
                        ->color('success')
                        ->icon('heroicon-o-check'),
                    Tables\Actions\BulkAction::make('تعطيل')
                        ->action(fn (\Illuminate\Support\Collection $records) => $records->each->update(['is_active' => false]))
                        ->requiresConfirmation()
                        ->color('danger')
                        ->icon('heroicon-o-x-mark'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\JobApplicationsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJobs::route('/'),
            'create' => Pages\CreateJob::route('/create'),
            'view' => Pages\ViewJob::route('/{record}'),
            'edit' => Pages\EditJob::route('/{record}/edit'),
        ];
    }
}
