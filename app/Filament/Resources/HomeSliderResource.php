<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HomeSliderResource\Pages;
use App\Models\HomeSlider;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Columns\ImageColumn;

class HomeSliderResource extends Resource
{
    protected static ?string $model = HomeSlider::class;
    protected static ?string $navigationIcon = 'heroicon-o-photo';
    protected static ?string $navigationLabel = 'شرائح الصفحة الرئيسية';
    protected static ?string $navigationGroup = 'إدارة المحتوى والمنتجات';
    protected static ?int $navigationSort = 7;
    protected static ?string $recordTitleAttribute = 'title_ar';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('معلومات الشريحة')
                    ->schema([
                        TextInput::make('title_ar')
                            ->label('العنوان (عربي)')
                            ->required(),
                        TextInput::make('title_en')
                            ->label('العنوان (إنجليزي)')
                            ->required(),
                        Textarea::make('description_ar')
                            ->label('الوصف (عربي)')
                            ->rows(3),
                        Textarea::make('description_en')
                            ->label('الوصف (إنجليزي)')
                            ->rows(3),
                        TextInput::make('button_text_ar')
                            ->label('نص الزر (عربي)'),
                        TextInput::make('button_text_en')
                            ->label('نص الزر (إنجليزي)'),
                        TextInput::make('button_link')
                            ->label('رابط الزر'),
                        FileUpload::make('image')
                            ->label('صورة الشريحة')
                            ->image()
                            ->required()
                            ->directory('sliders'),
                        TextInput::make('order')
                            ->label('الترتيب')
                            ->numeric()
                            ->default(1),
                        Toggle::make('is_active')
                            ->label('نشط')
                            ->default(true),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('image')
                    ->label('الصورة'),
                TextColumn::make('title_ar')
                    ->label('العنوان (عربي)')
                    ->searchable(),
                TextColumn::make('order')
                    ->label('الترتيب')
                    ->sortable(),
                ToggleColumn::make('is_active')
                    ->label('نشط'),
            ])
            ->defaultSort('order')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHomeSliders::route('/'),
            'create' => Pages\CreateHomeSlider::route('/create'),
            'edit' => Pages\EditHomeSlider::route('/{record}/edit'),
        ];
    }
}
