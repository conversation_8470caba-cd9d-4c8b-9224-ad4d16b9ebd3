<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TestimonialResource\Pages;
use App\Filament\Resources\TestimonialResource\RelationManagers;
use App\Models\Testimonial;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Collection;

class TestimonialResource extends Resource
{
    protected static ?string $model = Testimonial::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';

    protected static ?string $navigationLabel = 'آراء العملاء';

    protected static ?string $navigationGroup = 'إدارة المحتوى والمنتجات';

    protected static ?int $navigationSort = 6;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات العميل')
                    ->schema([
                        Forms\Components\TextInput::make('client_name_ar')
                            ->label('اسم العميل (عربي)')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('client_name_en')
                            ->label('اسم العميل (إنجليزي)')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('location_ar')
                            ->label('الموقع (عربي)')
                            ->maxLength(255),

                        Forms\Components\TextInput::make('location_en')
                            ->label('الموقع (إنجليزي)')
                            ->maxLength(255),

                        Forms\Components\FileUpload::make('image')
                            ->label('صورة العميل')
                            ->image()
                            ->directory('testimonials')
                            ->visibility('public')
                            ->imageEditor(),
                    ])->columns(2),

                Forms\Components\Section::make('محتوى الرأي')
                    ->schema([
                        Forms\Components\Textarea::make('content_ar')
                            ->label('المحتوى (عربي)')
                            ->required()
                            ->rows(3),

                        Forms\Components\Textarea::make('content_en')
                            ->label('المحتوى (إنجليزي)')
                            ->required()
                            ->rows(3),

                        Forms\Components\Radio::make('rating')
                            ->label('التقييم')
                            ->options([
                                5 => '5 نجوم',
                                4 => '4 نجوم',
                                3 => '3 نجوم',
                                2 => '2 نجمة',
                                1 => '1 نجمة',
                            ])
                            ->default(5)
                            ->inline()
                            ->required(),
                    ]),

                Forms\Components\Section::make('إعدادات الرأي')
                    ->schema([
                        Forms\Components\TextInput::make('order')
                            ->label('الترتيب')
                            ->numeric()
                            ->default(1)
                            ->minValue(1)
                            ->required(),

                        Forms\Components\Toggle::make('is_active')
                            ->label('نشط')
                            ->default(true)
                            ->required(),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order')
                    ->label('الترتيب')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('الحالة')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\ImageColumn::make('image')
                    ->label('الصورة')
                    ->circular(),

                Tables\Columns\TextColumn::make('client_name_ar')
                    ->label('اسم العميل (عربي)')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('client_name_en')
                    ->label('اسم العميل (إنجليزي)')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('location_ar')
                    ->label('الموقع (عربي)')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('location_en')
                    ->label('الموقع (إنجليزي)')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('rating')
                    ->label('التقييم')
                    ->formatStateUsing(fn (int $state): string => str_repeat('★', $state))
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('order')
            ->filters([
                Tables\Filters\SelectFilter::make('is_active')
                    ->label('الحالة')
                    ->options([
                        '1' => 'نشط',
                        '0' => 'غير نشط',
                    ]),

                Tables\Filters\SelectFilter::make('rating')
                    ->label('التقييم')
                    ->options([
                        '5' => '5 نجوم',
                        '4' => '4 نجوم',
                        '3' => '3 نجوم',
                        '2' => '2 نجمة',
                        '1' => '1 نجمة',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('تفعيل')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true]))
                        ->icon('heroicon-o-check-circle'),
                    Tables\Actions\BulkAction::make('تعطيل')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => false]))
                        ->icon('heroicon-o-x-circle'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTestimonials::route('/'),
            'create' => Pages\CreateTestimonial::route('/create'),
            'edit' => Pages\EditTestimonial::route('/{record}/edit'),
        ];
    }
}
