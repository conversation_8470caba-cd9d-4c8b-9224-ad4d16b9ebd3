<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PermissionResource\Pages;
use Spatie\Permission\Models\Permission;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;

class PermissionResource extends Resource
{
    protected static ?string $model = Permission::class;

    protected static ?string $navigationIcon = 'heroicon-o-key';
    protected static ?string $navigationLabel = 'الصلاحيات';
    protected static ?string $navigationGroup = 'إدارة النظام الأساسية';
    protected static ?int $navigationSort = 3;

    public static function getModelLabel(): string
    {
        return __('صلاحية');
    }

    public static function getPluralModelLabel(): string
    {
        return __('الصلاحيات');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('معلومات الصلاحية الأساسية')
                    ->description('قم بتعيين اسم الصلاحية ونوع الحارس المناسب')
                    ->icon('heroicon-o-key')
                    ->schema([
                        TextInput::make('name')
                            ->label('اسم الصلاحية (بالإنجليزية)')
                            ->helperText('اسم الصلاحية باللغة الإنجليزية (مثل: view_users, create_products)')
                            ->placeholder('مثال: view_users')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->regex('/^[a-z_]+$/')
                            ->validationMessages([
                                'regex' => 'يجب أن يحتوي اسم الصلاحية على أحرف إنجليزية صغيرة وشرطة سفلية فقط',
                            ]),

                        Select::make('guard_name')
                            ->label('نوع الحارس')
                            ->helperText('اختر نوع الحارس المناسب للصلاحية')
                            ->options([
                                'web' => 'واجهة الويب (Web)',
                                'api' => 'واجهة برمجة التطبيقات (API)',
                            ])
                            ->default('web')
                            ->required(),
                    ])->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('اسم الصلاحية')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->formatStateUsing(fn (string $state): string => static::translatePermissionName($state))
                    ->description(fn (Permission $record): string => $record->name),

                TextColumn::make('guard_name')
                    ->label('نوع الحارس')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'web' => 'واجهة الويب',
                        'api' => 'واجهة برمجة التطبيقات',
                        default => $state,
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'web' => 'success',
                        'api' => 'warning',
                        default => 'gray',
                    }),

                TextColumn::make('roles_count')
                    ->label('عدد الأدوار')
                    ->counts('roles')
                    ->badge()
                    ->color('primary')
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('آخر تحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('guard_name')
                    ->label('نوع الحارس')
                    ->options([
                        'web' => 'واجهة الويب',
                        'api' => 'واجهة برمجة التطبيقات',
                    ])
                    ->placeholder('جميع الأنواع'),
                
                Tables\Filters\Filter::make('has_roles')
                    ->label('الصلاحيات المستخدمة')
                    ->query(fn ($query) => $query->has('roles'))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض')
                    ->color('info'),
                Tables\Actions\EditAction::make()
                    ->label('تعديل')
                    ->color('warning'),
                Tables\Actions\DeleteAction::make()
                    ->label('حذف')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('حذف الصلاحية')
                    ->modalDescription('هل أنت متأكد من حذف هذه الصلاحية؟ سيتم إزالتها من جميع الأدوار.')
                    ->modalSubmitActionLabel('نعم، احذف')
                    ->modalCancelActionLabel('إلغاء')
                    ->before(function (Permission $record) {
                        // منع حذف الصلاحيات التي لها أدوار
                        if ($record->roles()->count() > 0) {
                            throw new \Exception('لا يمكن حذف هذه الصلاحية لأنها مُعيَّنة لـ ' . $record->roles()->count() . ' دور');
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد')
                        ->requiresConfirmation()
                        ->modalHeading('حذف الصلاحيات المحددة')
                        ->modalDescription('هل أنت متأكد من حذف الصلاحيات المحددة؟')
                        ->modalSubmitActionLabel('نعم، احذف')
                        ->modalCancelActionLabel('إلغاء')
                        ->before(function ($records) {
                            foreach ($records as $record) {
                                // منع حذف الصلاحيات التي لها أدوار
                                if ($record->roles()->count() > 0) {
                                    throw new \Exception('لا يمكن حذف الصلاحية "' . static::translatePermissionName($record->name) . '" لأنها مُعيَّنة لـ ' . $record->roles()->count() . ' دور');
                                }
                            }
                        }),
                ]),
            ])
            ->emptyStateHeading('لا توجد صلاحيات')
            ->emptyStateDescription('لم يتم إنشاء أي صلاحيات بعد. ابدأ بإنشاء صلاحية جديدة.')
            ->emptyStateIcon('heroicon-o-key');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPermissions::route('/'),
            'create' => Pages\CreatePermission::route('/create'),
            'edit' => Pages\EditPermission::route('/{record}/edit'),
        ];
    }

    /**
     * ترجمة اسم الصلاحية الواحدة
     */
    protected static function translatePermissionName(string $permissionName): string
    {
        // تقسيم اسم الصلاحية إلى أجزاء
        $parts = explode('_', $permissionName);
        
        if (count($parts) < 2) {
            return $permissionName;
        }

        $action = $parts[0]; // الإجراء (view, create, update, delete)
        $resource = implode('_', array_slice($parts, 1)); // اسم المورد

        // ترجمة الإجراءات
        $actionTranslations = [
            'view' => 'عرض',
            'view_any' => 'عرض جميع',
            'create' => 'إنشاء',
            'update' => 'تعديل',
            'delete' => 'حذف',
            'delete_any' => 'حذف جميع',
            'force_delete' => 'حذف نهائي',
            'force_delete_any' => 'حذف نهائي لجميع',
            'restore' => 'استعادة',
            'restore_any' => 'استعادة جميع',
            'replicate' => 'تكرار',
            'reorder' => 'إعادة ترتيب',
        ];

        // ترجمة الموارد
        $resourceTranslations = [
            'user' => 'المستخدمين',
            'role' => 'الأدوار',
            'product' => 'المنتجات',
            'category' => 'الفئات',
            'order' => 'الطلبات',
            'appointment' => 'المواعيد',
            'blog::post' => 'مقالات المدونة',
            'page' => 'الصفحات',
            'feature' => 'الميزات',
            'home::slider' => 'شرائح الصفحة الرئيسية',
            'job' => 'الوظائف',
            'job::application' => 'طلبات الوظائف',
            'language' => 'اللغات',
            'language::manager' => 'مدير اللغات',
            'metal::price' => 'أسعار المعادن',
            'metal::purity' => 'عيارات المعادن',
            'metal::type' => 'أنواع المعادن',
            'newsletter' => 'النشرة البريدية',
            'setting::change' => 'تغيير الإعدادات',
            'site::setting' => 'إعدادات الموقع',
            'store' => 'المتاجر',
            'super::admin::setting' => 'إعدادات السوبر أدمن',
            'testimonial' => 'الشهادات',
            'widget_StatsOverview' => 'ودجة الإحصائيات',
            'widget_SalesChart' => 'ودجة مخطط المبيعات',
            'widget_GoldPriceChart' => 'ودجة مخطط أسعار الذهب',
            'widget_LatestOrders' => 'ودجة أحدث الطلبات',
            'widget_UpcomingAppointments' => 'ودجة المواعيد القادمة',
            'widget_MaintenanceModeToggle' => 'ودجة وضع الصيانة',
            'page_DashboardPage' => 'صفحة لوحة التحكم',
            'page_PreviewSettings' => 'صفحة معاينة الإعدادات',
            'page_SearchSettings' => 'صفحة إعدادات البحث',
            'page_SiteSettingsManager' => 'صفحة مدير إعدادات الموقع',
            'page_TranslationsManager' => 'صفحة مدير الترجمات',
        ];

        $translatedAction = $actionTranslations[$action] ?? $action;
        $translatedResource = $resourceTranslations[$resource] ?? $resource;

        return "{$translatedAction} {$translatedResource}";
    }
}
