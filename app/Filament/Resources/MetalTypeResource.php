<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MetalTypeResource\Pages;
use App\Models\MetalType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Collection;

class MetalTypeResource extends Resource
{
    protected static ?string $model = MetalType::class;

    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?string $navigationLabel = 'أنواع المعادن';

    protected static ?string $modelLabel = 'نوع معدن';

    protected static ?string $pluralModelLabel = 'أنواع المعادن';

    protected static ?string $navigationGroup = 'إدارة المعادن المتخصصة';

    protected static ?int $navigationSort = 1;

    protected static ?string $recordTitleAttribute = 'name_ar';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات نوع المعدن')
                    ->description('أدخل تفاصيل نوع المعدن')
                    ->icon('heroicon-o-squares-2x2')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('الاسم بالإنجليزية')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(255)
                                    ->helperText('مثل: gold, silver, gold_coin'),

                                Forms\Components\TextInput::make('name_ar')
                                    ->label('الاسم بالعربية')
                                    ->required()
                                    ->maxLength(255)
                                    ->helperText('مثل: ذهب, فضة, جنيهات ذهبية'),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('icon')
                                    ->label('الأيقونة')
                                    ->maxLength(255)
                                    ->helperText('أيقونة FontAwesome مثل: fas fa-coins'),

                                Forms\Components\ColorPicker::make('color')
                                    ->label('اللون')
                                    ->default('#6b7280')
                                    ->helperText('لون المعدن في الواجهة'),
                            ]),

                        Forms\Components\Textarea::make('description')
                            ->label('الوصف')
                            ->rows(3)
                            ->columnSpanFull()
                            ->helperText('وصف مختصر عن نوع المعدن'),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('sort_order')
                                    ->label('ترتيب العرض')
                                    ->numeric()
                                    ->default(0)
                                    ->helperText('ترتيب ظهور المعدن (الأقل أولاً)'),

                                Forms\Components\Toggle::make('is_active')
                                    ->label('نشط')
                                    ->default(true)
                                    ->helperText('تفعيل/إلغاء تفعيل نوع المعدن'),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')
                    ->label('اسم المعدن')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('name')
                    ->label('الاسم الإنجليزي')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('gray'),

                Tables\Columns\TextColumn::make('purities_count')
                    ->label('عدد العيارات')
                    ->counts('purities')
                    ->badge()
                    ->color('info'),

                Tables\Columns\ColorColumn::make('color')
                    ->label('اللون'),

                Tables\Columns\TextColumn::make('sort_order')
                    ->label('الترتيب')
                    ->sortable()
                    ->alignCenter(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('sort_order')
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->color('primary'),
                    Tables\Actions\ReplicateAction::make()
                        ->label('نسخ')
                        ->color('warning'),
                    Tables\Actions\DeleteAction::make()
                        ->color('danger'),
                ])
                    ->label('إجراءات')
                    ->color('gray')
                    ->icon('heroicon-m-ellipsis-vertical')
                    ->size('sm'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد'),

                    Tables\Actions\BulkAction::make('activate')
                        ->label('تفعيل المحدد')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true]))
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading('تفعيل أنواع المعادن المحددة')
                        ->modalDescription('هل أنت متأكد من تفعيل جميع أنواع المعادن المحددة؟')
                        ->modalSubmitActionLabel('تفعيل'),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('إلغاء تفعيل المحدد')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => false]))
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->modalHeading('إلغاء تفعيل أنواع المعادن المحددة')
                        ->modalDescription('هل أنت متأكد من إلغاء تفعيل جميع أنواع المعادن المحددة؟')
                        ->modalSubmitActionLabel('إلغاء التفعيل'),
                ])
                    ->label('إجراءات مجمعة'),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make()
                    ->label('إضافة أول نوع معدن')
                    ->icon('heroicon-o-plus'),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMetalTypes::route('/'),
            'create' => Pages\CreateMetalType::route('/create'),
            'edit' => Pages\EditMetalType::route('/{record}/edit'),
        ];
    }
}
