<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CategoryResource\Pages;
use App\Models\Category;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Columns\ImageColumn;
use Illuminate\Support\Collection;

class CategoryResource extends Resource
{
    protected static ?string $model = Category::class;
    protected static ?string $navigationIcon = 'heroicon-o-tag';
    protected static ?string $navigationLabel = 'الفئات';
    protected static ?string $navigationGroup = 'إدارة المحتوى والمنتجات';
    protected static ?int $navigationSort = 1;
    protected static ?string $recordTitleAttribute = 'name_ar';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('معلومات الفئة')
                    ->schema([
                        TextInput::make('name_ar')
                            ->label('اسم الفئة (عربي)')
                            ->required(),
                        TextInput::make('name_en')
                            ->label('اسم الفئة (إنجليزي)')
                            ->required(),
                        Textarea::make('description_ar')
                            ->label('وصف الفئة (عربي)')
                            ->rows(3),
                        Textarea::make('description_en')
                            ->label('وصف الفئة (إنجليزي)')
                            ->rows(3),
                        FileUpload::make('image')
                            ->label('صورة الفئة')
                            ->image()
                            ->directory('categories')
                            ->maxSize(2048),
                        Select::make('parent_id')
                            ->label('الفئة الأم')
                            ->relationship('parent', 'name_ar')
                            ->searchable()
                            ->preload(),
                        TextInput::make('order')
                            ->label('الترتيب')
                            ->numeric()
                            ->default(0),
                        Toggle::make('is_active')
                            ->label('نشط')
                            ->default(true),
                        Toggle::make('show_price')
                            ->label('عرض السعر')
                            ->helperText('السماح بعرض أسعار المنتجات في هذه الفئة')
                            ->default(true),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name_ar')
                    ->label('اسم الفئة (عربي)')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('name_en')
                    ->label('اسم الفئة (إنجليزي)')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('parent.name_ar')
                    ->label('الفئة الأم')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('products_count')
                    ->label('عدد المنتجات')
                    ->counts('products')
                    ->sortable(),
                TextColumn::make('order')
                    ->label('الترتيب')
                    ->numeric()
                    ->sortable(),
                ImageColumn::make('image')
                    ->label('صورة الفئة')
                    ->circular(),
                ToggleColumn::make('is_active')
                    ->label('نشط'),
                ToggleColumn::make('show_price')
                    ->label('عرض السعر'),
                TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('parent_id')
                    ->label('الفئة الأم')
                    ->relationship('parent', 'name_ar')
                    ->searchable()
                    ->preload(),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('نشط'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('تفعيل')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true]))
                        ->icon('heroicon-o-check'),
                    Tables\Actions\BulkAction::make('تعطيل')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => false]))
                        ->icon('heroicon-o-x-mark'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            CategoryResource\RelationManagers\ProductsRelationManager::make(),
            CategoryResource\RelationManagers\ChildrenRelationManager::make(),
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCategories::route('/'),
            'create' => Pages\CreateCategory::route('/create'),
            'edit' => Pages\EditCategory::route('/{record}/edit'),
        ];
    }
}
