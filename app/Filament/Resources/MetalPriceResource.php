<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MetalPriceResource\Pages;
use App\Models\MetalPrice;
use App\Models\MetalType;
use App\Models\MetalPurity;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Filament\Notifications\Notification;
use App\Traits\HasPermissionFiltering;

class MetalPriceResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = MetalPrice::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'أسعار المعادن';

    protected static ?string $modelLabel = 'سعر معدن';

    protected static ?string $pluralModelLabel = 'أسعار المعادن';

    protected static ?string $navigationGroup = 'إدارة المعادن المتخصصة';

    protected static ?int $navigationSort = 3;

    protected static ?string $recordTitleAttribute = 'metal_type';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات سعر المعدن')
                    ->description('أدخل تفاصيل سعر المعدن بدقة')
                    ->icon('heroicon-o-banknotes')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('metal_type')
                                    ->label('نوع المعدن')
                                    ->options(MetalType::active()->ordered()->pluck('name_ar', 'name'))
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(fn (callable $set) => $set('purity', null))
                                    ->helperText('اختر نوع المعدن المراد تحديد سعره'),

                                Forms\Components\Select::make('purity')
                                    ->label('العيار/النقاء')
                                    ->options(function (callable $get) {
                                        $metalTypeName = $get('metal_type');
                                        if (!$metalTypeName) {
                                            return [];
                                        }

                                        $metalType = MetalType::where('name', $metalTypeName)->first();
                                        if (!$metalType) {
                                            return [];
                                        }

                                        return MetalPurity::where('metal_type_id', $metalType->id)
                                            ->active()
                                            ->ordered()
                                            ->pluck('name_ar', 'name');
                                    })
                                    ->required()
                                    ->helperText('اختر العيار المناسب للمعدن المحدد'),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('price_per_gram')
                                    ->label('السعر لكل جرام')
                                    ->numeric()
                                    ->prefix('ج.م')
                                    ->step(0.01)
                                    ->minValue(0.01)
                                    ->maxValue(999999.99)
                                    ->required()
                                    ->helperText('السعر بالجنيه المصري لكل جرام'),

                                Forms\Components\TextInput::make('price_per_piece')
                                    ->label('السعر لكل قطعة')
                                    ->numeric()
                                    ->prefix('ج.م')
                                    ->step(0.01)
                                    ->minValue(0.01)
                                    ->maxValue(999999.99)
                                    ->visible(function (callable $get) {
                                        return $get('metal_type') === 'gold_coin';
                                    })
                                    ->helperText('السعر بالجنيه المصري لكل قطعة (للجنيهات الذهبية فقط)'),
                            ]),
                    ]),

                Forms\Components\Toggle::make('is_active')
                    ->label('نشط')
                    ->default(true)
                    ->helperText('تفعيل/إلغاء تفعيل هذا السعر'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('metal_type')
                    ->label('نوع المعدن')
                    ->formatStateUsing(function (string $state): string {
                        $metalType = MetalType::where('name', $state)->first();
                        return $metalType ? $metalType->name_ar : $state;
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'gold' => 'warning',
                        'silver' => 'gray',
                        'gold_coin' => 'success',
                        default => 'secondary',
                    })
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('purity')
                    ->label('العيار')
                    ->formatStateUsing(function (string $state, $record): string {
                        $metalType = MetalType::where('name', $record->metal_type)->first();
                        if ($metalType) {
                            $purity = MetalPurity::where('metal_type_id', $metalType->id)
                                ->where('name', $state)
                                ->first();
                            return $purity ? $purity->name_ar : $state;
                        }
                        return $state;
                    })
                    ->badge()
                    ->color('success')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('price_per_gram')
                    ->label('سعر الجرام')
                    ->money('EGP', locale: 'ar')
                    ->sortable()
                    ->searchable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('price_per_piece')
                    ->label('سعر القطعة')
                    ->money('EGP', locale: 'ar')
                    ->sortable()
                    ->searchable()
                    ->weight('bold')
                    ->placeholder('غير محدد')
                    ->visible(fn ($record) => $record && $record->metal_type === 'gold_coin'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ السعر')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->description(fn (MetalPrice $record): string =>
                        $record->created_at->diffForHumans()
                    ),

                Tables\Columns\TextColumn::make('source')
                    ->label('المصدر')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'manual' => 'primary',
                        'api' => 'success',
                        'import' => 'warning',
                        'isagha_api' => 'info',
                        'test_data' => 'secondary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'manual' => 'يدوي',
                        'api' => 'API',
                        'import' => 'استيراد',
                        'test_data' => 'بيانات تجريبية',
                        'isagha_api' => 'iSagha API',
                        default => $state,
                    })
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('آخر تحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('metal_type')
                    ->label('نوع المعدن')
                    ->options(MetalType::active()->ordered()->pluck('name_ar', 'name'))
                    ->placeholder('جميع الأنواع'),

                Tables\Filters\SelectFilter::make('purity')
                    ->label('العيار')
                    ->options(function () {
                        $options = [];
                        $metalTypes = MetalType::active()->with('purities')->get();

                        foreach ($metalTypes as $metalType) {
                            foreach ($metalType->purities as $purity) {
                                $options[$purity->name] = $purity->name_ar;
                            }
                        }

                        return $options;
                    })
                    ->placeholder('جميع العيارات'),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('الحالة')
                    ->placeholder('جميع الأسعار')
                    ->trueLabel('نشط فقط')
                    ->falseLabel('غير نشط فقط'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->color('primary'),
                    Tables\Actions\ReplicateAction::make()
                        ->label('نسخ')
                        ->color('warning'),
                    Tables\Actions\DeleteAction::make()
                        ->color('danger'),
                ])
                    ->label('إجراءات')
                    ->color('gray')
                    ->icon('heroicon-m-ellipsis-vertical')
                    ->size('sm'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد'),

                    Tables\Actions\BulkAction::make('activate')
                        ->label('تفعيل المحدد')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true]))
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading('تفعيل الأسعار المحددة')
                        ->modalDescription('هل أنت متأكد من تفعيل جميع الأسعار المحددة؟')
                        ->modalSubmitActionLabel('تفعيل'),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('إلغاء تفعيل المحدد')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => false]))
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->modalHeading('إلغاء تفعيل الأسعار المحددة')
                        ->modalDescription('هل أنت متأكد من إلغاء تفعيل جميع الأسعار المحددة؟')
                        ->modalSubmitActionLabel('إلغاء التفعيل'),


                ])
                    ->label('إجراءات مجمعة'),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make()
                    ->label('إضافة أول سعر')
                    ->icon('heroicon-o-plus'),
            ])
            ->headerActions([
                Tables\Actions\Action::make('fetch_isagha_prices')
                    ->label('جلب الأسعار من iSagha')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function () {
                        // جلب البيانات من iSagha
                        $processedPrices = static::fetchAndProcessISaghaPrices();

                        if (empty($processedPrices)) {
                            return;
                        }

                        // عرض البيانات للمراجعة
                        return static::showReviewModal($processedPrices);
                    })
                    ->requiresConfirmation()
                    ->modalHeading('جلب الأسعار من iSagha')
                    ->modalDescription('هل تريد جلب أحدث الأسعار من موقع iSagha؟ سيتم عرض الأسعار للمراجعة قبل الحفظ.')
                    ->modalSubmitActionLabel('جلب الأسعار')
                    ->modalCancelActionLabel('إلغاء'),

                // Tables\Actions\Action::make('debug_scraping')
                //     ->label('تشخيص الجلب')
                //     ->icon('heroicon-o-bug-ant')
                //     ->color('info')
                //     ->action(function () {
                //         // جلب HTML من iSagha
                //         $response = Http::timeout(30)
                //             ->withHeaders([
                //                 'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                //                 'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                //                 'Accept-Language' => 'ar,en-US;q=0.7,en;q=0.3',
                //             ])
                //             ->get('https://market.isagha.com/prices');

                //         if ($response->successful()) {
                //             $html = $response->body();
                //             $allPrices = static::extractAllPricesFromHTML($html);

                //             // حفظ النتائج في الجلسة لعرضها
                //             session(['debug_scraped_prices' => $allPrices]);

                //             Notification::make()
                //                 ->title('تم استخراج الأسعار للتشخيص')
                //                 ->body('تم استخراج ' . count($allPrices) . ' سعر من الموقع.')
                //                 ->info()
                //                 ->actions([
                //                     \Filament\Notifications\Actions\Action::make('view_debug')
                //                         ->label('عرض جميع الأسعار')
                //                         ->url(static::getUrl('debug-prices'))
                //                         ->button(),
                //                 ])
                //                 ->send();
                //         } else {
                //             Notification::make()
                //                 ->title('فشل في جلب البيانات')
                //                 ->body('لم يتم الاتصال بموقع iSagha بنجاح.')
                //                 ->danger()
                //                 ->send();
                //         }
                //     })
                //     ->requiresConfirmation()
                //     ->modalHeading('تشخيص جلب البيانات')
                //     ->modalDescription('هذا سيجلب البيانات من iSagha ويعرض جميع الأسعار المستخرجة للتشخيص.')
                //     ->modalSubmitActionLabel('تشخيص')
                //     ->modalCancelActionLabel('إلغاء'),

                // Tables\Actions\Action::make('view_all_prices')
                //     ->label('عرض جميع الأسعار المستخرجة')
                //     ->icon('heroicon-o-eye')
                //     ->color('info')
                //     ->url(static::getUrl('debug-prices'))
                //     ->openUrlInNewTab(false),


                ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMetalPrices::route('/'),
            'create' => Pages\CreateMetalPrice::route('/create'),
            'edit' => Pages\EditMetalPrice::route('/{record}/edit'),
            'review-isagha-prices' => Pages\ReviewISaghaPrices::route('/review-isagha-prices'),
            'missing-purities' => Pages\MissingPurities::route('/missing-purities'),
            'debug-prices' => Pages\DebugPrices::route('/debug-prices'),
        ];
    }

    /**
     * جلب ومعالجة الأسعار من iSagha API
     */
    public static function fetchAndProcessISaghaPrices()
    {
        try {
            // جلب البيانات الحقيقية من موقع iSagha عبر web scraping
            $scrapedData = static::scrapeISaghaPrices();

            Log::info('Scraped iSagha data:', ['data' => $scrapedData]);

            if (empty($scrapedData)) {
                Notification::make()
                    ->title('لا توجد بيانات')
                    ->body('فشل في جلب البيانات من موقع iSagha. يرجى المحاولة لاحقاً.')
                    ->warning()
                    ->send();
                return [];
            }

            // معالجة البيانات المجلبة (البيانات تأتي من parseISaghaHTML بالتنسيق الصحيح)
            $processedPrices = static::processScrapedPrices($scrapedData);

            if (empty($processedPrices)) {
                Notification::make()
                    ->title('لا توجد أسعار متوافقة')
                    ->body('تم جلب البيانات من iSagha ولكن لم يتم العثور على أسعار متوافقة مع النظام.')
                    ->warning()
                    ->send();
                return [];
            }

            return $processedPrices;

        } catch (\Exception $e) {
            Notification::make()
                ->title('خطأ في النظام')
                ->body('حدث خطأ أثناء جلب الأسعار: ' . $e->getMessage())
                ->danger()
                ->send();
            return [];
        }
    }

    /**
     * جلب البيانات الحقيقية من موقع iSagha عبر web scraping
     */
    private static function scrapeISaghaPrices()
    {
        try {
            // جلب محتوى الصفحة
            $response = Http::timeout(30)
                ->withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'ar,en-US;q=0.7,en;q=0.3',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                ])
                ->get('https://market.isagha.com/prices');

            if (!$response->successful()) {
                Log::error('Failed to fetch iSagha page', ['status' => $response->status()]);
                return [];
            }

            $html = $response->body();
            Log::info('iSagha HTML fetched', ['length' => strlen($html)]);

            // استخراج الأسعار من HTML
            $prices = static::parseISaghaHTML($html);

            return $prices;

        } catch (\Exception $e) {
            Log::error('Error scraping iSagha prices', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * تحليل HTML واستخراج الأسعار بناءً على المعادن والعيارات الموجودة في النظام
     */
    private static function parseISaghaHTML($html)
    {
        $prices = [];

        try {
            Log::info("Starting HTML parsing", ['html_length' => strlen($html)]);

            // أولاً: استخراج جميع الأسعار من الموقع بطريقة عامة
            $allFoundPrices = static::extractAllPricesFromHTML($html);
            Log::info("All prices found in HTML", ['count' => count($allFoundPrices), 'prices' => $allFoundPrices]);

            // ثانياً: الحصول على جميع أنواع المعادن والعيارات من النظام
            $metalTypes = MetalType::active()->with('purities')->get();

            foreach ($metalTypes as $metalType) {
                // تخطي الجنيهات الذهبية في الجلب الفعلي
                if ($metalType->name === 'gold_coin') {
                    Log::info("Skipping gold_coin type - not fetched from iSagha", ['metal' => $metalType->name]);
                    continue;
                }

                Log::info("Processing metal type", ['metal' => $metalType->name, 'name_ar' => $metalType->name_ar]);

                foreach ($metalType->purities as $purity) {
                    Log::info("Processing purity", ['purity' => $purity->name, 'name_ar' => $purity->name_ar]);

                    // البحث عن هذا العيار في الأسعار المستخرجة
                    $foundPrice = static::findPriceForPurity($allFoundPrices, $purity->name, $purity->name_ar, $metalType->name);

                    if ($foundPrice && static::isPriceReasonable($metalType->name, $foundPrice)) {
                        $prices[] = [
                            'name' => $metalType->name_ar . ' عيار ' . $purity->name_ar,
                            'metal' => $metalType->name,
                            'purity' => $purity->name,
                            'price' => $foundPrice,
                        ];
                        Log::info("Found price for purity", [
                            'metal' => $metalType->name,
                            'purity' => $purity->name,
                            'price' => $foundPrice
                        ]);
                    } else {
                        Log::info("No price found for purity", [
                            'metal' => $metalType->name,
                            'purity' => $purity->name
                        ]);
                    }
                }
            }

            // إذا لم نجد أسعار، نحاول patterns أخرى
            if (empty($prices)) {
                Log::info('No prices found with specific patterns, trying alternative methods');

                // محاولة البحث عن الأسعار في جدول HTML
                if (preg_match_all('/<td[^>]*>([0-9,]+\.?[0-9]*)\s*ج\.م<\/td>/u', $html, $tablePrices)) {
                    Log::info('Found table prices', ['prices' => $tablePrices[1]]);

                    foreach ($tablePrices[1] as $priceText) {
                        $price = floatval(str_replace(',', '', $priceText));
                        if ($price > 1000) {
                            // هذا على الأرجح سعر ذهب
                            $prices[] = [
                                'name' => 'ذهب (مستخرج من جدول)',
                                'metal' => 'gold',
                                'purity' => '21K', // افتراضي
                                'price' => $price,
                            ];
                        } elseif ($price > 10 && $price < 200) {
                            // هذا على الأرجح سعر فضة
                            $prices[] = [
                                'name' => 'فضة (مستخرج من جدول)',
                                'metal' => 'silver',
                                'purity' => '925', // افتراضي
                                'price' => $price,
                            ];
                        }
                    }
                }

                // محاولة أخرى: البحث عن أي أرقام مع "ج.م"
                if (empty($prices)) {
                    preg_match_all('/([0-9,]+\.?[0-9]*)\s*ج\.م/u', $html, $allPrices);

                    if (!empty($allPrices[1])) {
                        Log::info('Found all potential prices', ['count' => count($allPrices[1]), 'first_10' => array_slice($allPrices[1], 0, 10)]);

                        $foundPrices = array_map(function($price) {
                            return floatval(str_replace(',', '', $price));
                        }, $allPrices[1]);

                        // فلترة وتصنيف الأسعار
                        $goldPrices = array_filter($foundPrices, function($price) {
                            return $price >= 1500 && $price <= 8000; // نطاق أسعار الذهب المتوقع
                        });

                        $silverPrices = array_filter($foundPrices, function($price) {
                            return $price >= 20 && $price <= 100; // نطاق أسعار الفضة المتوقع
                        });

                        // إضافة أسعار الذهب
                        $goldPurities = ['24K', '22K', '21K', '18K', '14K'];
                        $goldPricesArray = array_values($goldPrices);
                        foreach ($goldPurities as $index => $purity) {
                            if (isset($goldPricesArray[$index])) {
                                $prices[] = [
                                    'name' => 'ذهب عيار ' . str_replace('K', '', $purity),
                                    'metal' => 'gold',
                                    'purity' => $purity,
                                    'price' => $goldPricesArray[$index],
                                ];
                            }
                        }

                        // إضافة أسعار الفضة
                        $silverPurities = ['999', '925', '900'];
                        $silverPricesArray = array_values($silverPrices);
                        foreach ($silverPurities as $index => $purity) {
                            if (isset($silverPricesArray[$index])) {
                                $prices[] = [
                                    'name' => 'فضة عيار ' . $purity,
                                    'metal' => 'silver',
                                    'purity' => $purity,
                                    'price' => $silverPricesArray[$index],
                                ];
                            }
                        }
                    }
                }
            }

            // تحليل العيارات المفقودة
            $missingPurities = static::findMissingPurities($allFoundPrices, $metalTypes);
            if (!empty($missingPurities)) {
                Log::info('Found missing purities', ['missing' => $missingPurities]);
                session(['missing_purities_from_isagha' => $missingPurities]);
            }

            Log::info('Parsed prices from HTML', [
                'count' => count($prices),
                'prices' => $prices,
                'missing_purities_count' => count($missingPurities)
            ]);
            return $prices;

        } catch (\Exception $e) {
            Log::error('Error parsing iSagha HTML', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * معالجة الأسعار المستخرجة من iSagha وتحويلها للتنسيق المطلوب
     */
    private static function processScrapedPrices($scrapedPrices)
    {
        $processedPrices = [];

        foreach ($scrapedPrices as $priceData) {
            Log::info('Processing scraped price:', $priceData);

            // الحصول على السعر الحالي من قاعدة البيانات
            $currentPrice = MetalPrice::where('metal_type', $priceData['metal'])
                ->where('purity', $priceData['purity'])
                ->where('currency', 'EGP')
                ->where('is_active', true)
                ->first();

            // الحصول على معلومات نوع المعدن والعيار
            $metalType = MetalType::where('name', $priceData['metal'])->first();
            $purity = null;

            if ($metalType) {
                $purity = MetalPurity::where('metal_type_id', $metalType->id)
                    ->where('name', $priceData['purity'])
                    ->first();
            }

            if (!$metalType) {
                Log::warning('Metal type not found in database:', [
                    'metal' => $priceData['metal'],
                    'price_data' => $priceData
                ]);
                continue;
            }

            if (!$purity) {
                Log::warning('Purity not found in database:', [
                    'metal' => $priceData['metal'],
                    'purity' => $priceData['purity'],
                    'price_data' => $priceData
                ]);
                continue;
            }

            $currentPriceValue = $currentPrice ? $currentPrice->price_per_gram : 0;
            $newPrice = $priceData['price'];
            $difference = $newPrice - $currentPriceValue;
            $differencePercent = $currentPriceValue > 0 ? (($difference / $currentPriceValue) * 100) : 0;

            $processedPrices[] = [
                'metal_type' => $priceData['metal'],
                'metal_type_name' => $metalType->name_ar,
                'purity' => $priceData['purity'],
                'purity_name' => $purity->name_ar,
                'new_price' => $newPrice,
                'current_price' => $currentPriceValue,
                'difference' => $difference,
                'difference_percent' => $differencePercent,
                'formatted_new_price' => number_format($newPrice, 2),
                'formatted_current_price' => number_format($currentPriceValue, 2),
                'formatted_difference' => number_format($difference, 2),
                'formatted_difference_percent' => number_format($differencePercent, 1),
                'direction' => $difference > 0 ? 'up' : ($difference < 0 ? 'down' : 'stable'),
                'source' => 'isagha_scraping',
            ];
        }

        Log::info('Processed scraped prices count:', ['count' => count($processedPrices)]);
        return $processedPrices;
    }

    /**
     * معالجة بيانات iSagha وتحويلها للتنسيق المطلوب (للاستخدام مع API)
     */
    private static function processISaghaData($data)
    {
        $processedPrices = [];

        // تسجيل هيكل البيانات للتشخيص
        Log::info('Processing iSagha data structure:', [
            'data_type' => gettype($data),
            'data_keys' => is_array($data) ? array_keys($data) : 'not_array',
            'first_item' => is_array($data) && !empty($data) ? $data[0] ?? 'no_first_item' : 'empty_or_not_array'
        ]);

        // التعامل مع تنسيقات مختلفة من البيانات
        $items = [];

        if (is_array($data)) {
            // إذا كانت البيانات مصفوفة مباشرة
            $items = $data;
        } elseif (is_object($data) || (is_array($data) && isset($data['data']))) {
            // إذا كانت البيانات داخل كائن أو مصفوفة بمفتاح 'data'
            $items = $data['data'] ?? $data['prices'] ?? $data['metals'] ?? [];
        }

        if (empty($items)) {
            Log::warning('No items found in iSagha data');
            return [];
        }

        // خريطة تحويل أسماء المعادن والعيارات من iSagha إلى النظام
        $metalMapping = [
            'gold' => 'gold',
            'silver' => 'silver',
            'ذهب' => 'gold',
            'فضة' => 'silver',
            'الذهب' => 'gold',
            'الفضة' => 'silver',
            'Gold' => 'gold',
            'Silver' => 'silver',
        ];

        $purityMapping = [
            // عيارات الذهب
            '24' => '24K',
            '22' => '22K',
            '21' => '21K',
            '18' => '18K',
            '14' => '14K',
            '12' => '12K',
            '9' => '9K',
            '24K' => '24K',
            '22K' => '22K',
            '21K' => '21K',
            '18K' => '18K',
            '14K' => '14K',
            '12K' => '12K',
            '9K' => '9K',
            // عيارات الفضة
            '999' => '999',
            '925' => '925',
            '900' => '900',
            '800' => '800',
            '600' => '600',
        ];

        foreach ($items as $item) {
            Log::info('Processing item:', ['item' => $item]);

            // تحديد نوع المعدن
            $metalType = null;
            $searchFields = ['name', 'metal', 'type', 'metal_type', 'title'];

            foreach ($metalMapping as $key => $value) {
                foreach ($searchFields as $field) {
                    if (isset($item[$field]) && stripos($item[$field], $key) !== false) {
                        $metalType = $value;
                        break 2;
                    }
                }
            }

            if (!$metalType) {
                Log::info('Metal type not found for item:', ['item' => $item]);
                continue;
            }

            // تحديد العيار
            $purity = null;
            $purityFields = ['purity', 'karat', 'grade', 'name', 'title'];

            foreach ($purityMapping as $key => $value) {
                foreach ($purityFields as $field) {
                    if (isset($item[$field]) && (
                        stripos($item[$field], $key) !== false ||
                        $item[$field] == $key
                    )) {
                        $purity = $value;
                        break 2;
                    }
                }
            }

            if (!$purity) {
                Log::info('Purity not found for item:', ['item' => $item]);
                continue;
            }

            // التحقق من وجود نوع المعدن والعيار في النظام
            $metalTypeRecord = MetalType::where('name', $metalType)->first();
            if (!$metalTypeRecord) {
                Log::info('Metal type not found in database:', ['metal_type' => $metalType]);
                continue;
            }

            $purityRecord = MetalPurity::where('metal_type_id', $metalTypeRecord->id)
                ->where('name', $purity)
                ->first();
            if (!$purityRecord) {
                Log::info('Purity not found in database:', ['metal_type' => $metalType, 'purity' => $purity]);
                continue;
            }

            // الحصول على السعر الحالي
            $currentPrice = MetalPrice::where('metal_type', $metalType)
                ->where('purity', $purity)
                ->where('currency', 'EGP')
                ->where('is_active', true)
                ->first();

            // البحث عن السعر في حقول مختلفة
            $priceFields = ['price', 'price_per_gram', 'sell_price', 'buy_price', 'value'];
            $newPrice = 0;

            foreach ($priceFields as $field) {
                if (isset($item[$field]) && is_numeric($item[$field])) {
                    $newPrice = floatval($item[$field]);
                    break;
                }
            }

            if ($newPrice <= 0) {
                Log::info('Price not found or invalid for item:', ['item' => $item]);
                continue;
            }

            $currentPriceValue = $currentPrice ? $currentPrice->price_per_gram : 0;
            $difference = $newPrice - $currentPriceValue;
            $differencePercent = $currentPriceValue > 0 ? (($difference / $currentPriceValue) * 100) : 0;

            $processedPrices[] = [
                'metal_type' => $metalType,
                'metal_type_name' => $metalTypeRecord->name_ar,
                'purity' => $purity,
                'purity_name' => $purityRecord->name_ar,
                'new_price' => $newPrice,
                'current_price' => $currentPriceValue,
                'difference' => $difference,
                'difference_percent' => $differencePercent,
                'formatted_new_price' => number_format($newPrice, 2),
                'formatted_current_price' => number_format($currentPriceValue, 2),
                'formatted_difference' => number_format($difference, 2),
                'formatted_difference_percent' => number_format($differencePercent, 1),
                'direction' => $difference > 0 ? 'up' : ($difference < 0 ? 'down' : 'stable'),
            ];
        }

        Log::info('Processed prices count:', ['count' => count($processedPrices)]);
        return $processedPrices;
    }



    /**
     * حفظ الأسعار الجديدة مع تطبيق نظام التفعيل التلقائي
     */
    public static function savePrices($processedPrices)
    {
        $savedCount = 0;
        $errors = [];

        foreach ($processedPrices as $priceData) {
            try {
                // تحديد المصدر
                $source = isset($priceData['source']) ? $priceData['source'] : 'isagha_api';

                // إنشاء السعر الجديد
                $newPrice = MetalPrice::create([
                    'metal_type' => $priceData['metal_type'],
                    'purity' => $priceData['purity'],
                    'price_per_gram' => $priceData['new_price'],
                    'price_per_ounce' => $priceData['new_price'] * 31.1035,
                    'currency' => 'EGP',
                    'price_date' => now(),
                    'source' => $source,
                    'is_active' => true,
                ]);

                // تطبيق نظام التفعيل التلقائي
                static::applyAutoActivation($newPrice);

                $savedCount++;

            } catch (\Exception $e) {
                $errors[] = "خطأ في حفظ {$priceData['metal_type_name']} {$priceData['purity_name']}: " . $e->getMessage();
            }
        }

        // إرسال إشعار بالنتيجة
        if ($savedCount > 0) {
            $metalTypes = collect($processedPrices)->groupBy('metal_type_name')->keys()->implode(', ');

            Notification::make()
                ->title('تم حفظ الأسعار بنجاح')
                ->body("تم حفظ {$savedCount} سعر من iSagha للمعادن: {$metalTypes}. تم تطبيق نظام التفعيل التلقائي.")
                ->success()
                ->duration(10000)
                ->send();
        }

        if (!empty($errors)) {
            Notification::make()
                ->title('بعض الأخطاء في الحفظ')
                ->body(implode("\n", array_slice($errors, 0, 3)))
                ->warning()
                ->duration(15000)
                ->send();
        }

        // إذا لم يتم حفظ أي أسعار
        if ($savedCount === 0 && empty($errors)) {
            Notification::make()
                ->title('لم يتم حفظ أي أسعار')
                ->body('لم يتم العثور على أسعار صالحة للحفظ من iSagha.')
                ->warning()
                ->send();
        }
    }

    /**
     * تطبيق نظام التفعيل التلقائي
     * يجعل السعر الجديد مفعل ويلغي تفعيل الأسعار الأخرى لنفس العيار
     */
    public static function applyAutoActivation(MetalPrice $newPrice)
    {
        // إلغاء تفعيل جميع الأسعار الأخرى لنفس المعدن والعيار
        MetalPrice::where('metal_type', $newPrice->metal_type)
            ->where('purity', $newPrice->purity)
            ->where('currency', $newPrice->currency)
            ->where('id', '!=', $newPrice->id)
            ->update(['is_active' => false]);

        // التأكد من أن السعر الجديد مفعل
        $newPrice->update(['is_active' => true]);
    }

    /**
     * عرض صفحة مراجعة الأسعار قبل الحفظ
     */
    public static function showReviewModal($processedPrices)
    {
        // حفظ البيانات في الجلسة
        session(['pending_isagha_prices' => $processedPrices]);

        // التحقق من وجود عيارات مفقودة
        $missingPurities = session('missing_purities_from_isagha', []);
        $totalExtracted = session('debug_scraped_prices', []);
        $totalExtractedCount = count($totalExtracted);

        // إنشاء رسالة الإشعار
        $message = 'تم جلب ' . count($processedPrices) . ' سعر من iSagha';
        if ($totalExtractedCount > count($processedPrices)) {
            $message .= ' من أصل ' . $totalExtractedCount . ' سعر مستخرج';
        }

        if (!empty($missingPurities)) {
            $message .= '. تم العثور على ' . count($missingPurities) . ' عيار غير موجود في النظام';
        }

        $message .= '. سيتم توجيهك لصفحة المراجعة.';

        // إرسال إشعار
        $notification = Notification::make()
            ->title('تم جلب الأسعار بنجاح')
            ->body($message)
            ->success();

        // إضافة إجراءات إضافية إذا كانت هناك عيارات مفقودة
        if (!empty($missingPurities)) {
            $notification->actions([
                \Filament\Notifications\Actions\Action::make('view_missing')
                    ->label('عرض العيارات المفقودة')
                    ->url(static::getUrl('missing-purities'))
                    ->button()
                    ->color('warning'),
            ]);
        }

        $notification->send();

        // التوجيه لصفحة المراجعة
        return redirect()->to(static::getUrl('review-isagha-prices'));
    }





    /**
     * التحقق من معقولية السعر حسب نوع المعدن
     */
    private static function isPriceReasonable($metalType, $price)
    {
        $priceRanges = [
            'gold' => ['min' => 1000, 'max' => 10000],
            'silver' => ['min' => 10, 'max' => 200],
            'gold_coin' => ['min' => 1000, 'max' => 10000],
            'platinum' => ['min' => 500, 'max' => 5000],
        ];

        $range = $priceRanges[$metalType] ?? ['min' => 1, 'max' => 100000];

        return $price >= $range['min'] && $price <= $range['max'];
    }

    /**
     * استخراج جميع الأسعار من HTML بطريقة شاملة
     */
    public static function extractAllPricesFromHTML($html)
    {
        $foundPrices = [];

        try {
            // إزالة الـ HTML tags للحصول على النص فقط
            $text = strip_tags($html);

            // Pattern 1: البحث عن أسعار العيارات المباشرة مع التصنيف الصحيح
            preg_match_all('/عيار\s*(\d+)\s*([0-9,]+\.?[0-9]*)\s*ج\.م/u', $text, $matches1, PREG_SET_ORDER);

            foreach ($matches1 as $match) {
                $purity = trim($match[1]);
                $price = floatval(str_replace(',', '', $match[2]));

                if ($price > 0) {
                    // تصنيف صحيح حسب العيار والسعر
                    if (intval($purity) <= 24 && $price >= 1000) {
                        // عيارات الذهب (24, 22, 21, 18, 14, 12, 9)
                        $foundPrices[] = [
                            'purity_text' => 'ذهب عيار ' . $purity,
                            'price' => $price,
                            'context' => 'عيار ' . $purity . ' - ' . $price . ' ج.م'
                        ];
                    } elseif (intval($purity) >= 600 && $price < 200) {
                        // عيارات الفضة (999, 925, 900, 800, 600)
                        $foundPrices[] = [
                            'purity_text' => 'فضة عيار ' . $purity,
                            'price' => $price,
                            'context' => 'عيار ' . $purity . ' - ' . $price . ' ج.م'
                        ];
                    }
                }
            }

            // Pattern 2: البحث عن أسعار الفضة بطرق متعددة
            // Pattern 2a: عيار 999 59 ج.م بيع (سعر البيع فقط)
            preg_match_all('/عيار\s*(\d{3})\s*([0-9,]+\.?[0-9]*)\s*ج\.م\s*بيع/u', $text, $matches2a, PREG_SET_ORDER);

            foreach ($matches2a as $match) {
                $purity = trim($match[1]);
                $price = floatval(str_replace(',', '', $match[2]));

                if ($price > 0 && intval($purity) >= 600) { // عيارات الفضة
                    $foundPrices[] = [
                        'purity_text' => 'فضة عيار ' . $purity,
                        'price' => $price,
                        'context' => 'عيار ' . $purity . ' - ' . $price . ' ج.م بيع'
                    ];
                }
            }

            // Pattern 2b: البحث المباشر عن عيارات الفضة الشائعة (سعر البيع فقط)
            $silverPurities = ['999', '925', '900', '800', '600'];
            foreach ($silverPurities as $purity) {
                // البحث عن سعر البيع فقط: 999 59 ج.م بيع
                if (preg_match('/' . $purity . '\s*([0-9,]+\.?[0-9]*)\s*ج\.م\s*بيع/u', $text, $match)) {
                    $price = floatval(str_replace(',', '', $match[1]));
                    if ($price > 0 && $price < 200) {
                        $foundPrices[] = [
                            'purity_text' => 'فضة عيار ' . $purity,
                            'price' => $price,
                            'context' => $purity . ' - ' . $price . ' ج.م بيع'
                        ];
                    }
                }

                // البحث عن: عيار 999 59 ج.م بيع
                if (preg_match('/عيار\s*' . $purity . '\s*([0-9,]+\.?[0-9]*)\s*ج\.م\s*بيع/u', $text, $match)) {
                    $price = floatval(str_replace(',', '', $match[1]));
                    if ($price > 0 && $price < 200) {
                        $foundPrices[] = [
                            'purity_text' => 'فضة عيار ' . $purity,
                            'price' => $price,
                            'context' => 'عيار ' . $purity . ' - ' . $price . ' ج.م بيع'
                        ];
                    }
                }
            }

            // Pattern 3: البحث عن الجنيهات الذهبية (للتشخيص فقط)
            // Pattern 3a: جنيه ذهب 37400 ج.م بيع
            preg_match_all('/جنيه\s*ذهب\s*([0-9,]+\.?[0-9]*)\s*ج\.م\s*بيع/u', $text, $matches3a, PREG_SET_ORDER);

            foreach ($matches3a as $match) {
                $price = floatval(str_replace(',', '', $match[1]));

                if ($price >= 35000 && $price <= 45000) { // نطاق منطقي للجنيه الذهبي
                    $foundPrices[] = [
                        'purity_text' => 'جنيه ذهب',
                        'price' => $price,
                        'context' => 'جنيه ذهب - ' . $price . ' ج.م بيع'
                    ];
                }
            }





            // Pattern 6: البحث المباشر في النص عن الأسعار بجانب العيارات
            // مثل: "عيار 24 5342.75 ج.م بيع"
            preg_match_all('/(\d+)\s*([0-9,]+\.?[0-9]*)\s*ج\.م\s*بيع/u', $text, $matches6, PREG_SET_ORDER);

            foreach ($matches6 as $match) {
                $purity = trim($match[1]);
                $price = floatval(str_replace(',', '', $match[2]));

                if ($price > 0) {
                    if (intval($purity) <= 24) {
                        $type = 'ذهب عيار ' . $purity;
                    } else {
                        $type = 'فضة عيار ' . $purity;
                    }

                    $foundPrices[] = [
                        'purity_text' => $type,
                        'price' => $price,
                        'context' => $type . ' - ' . $price . ' ج.م بيع'
                    ];
                }
            }

            // Pattern 7: البحث الخاص بالجنيهات الذهبية (من البيانات المرئية)
            // البحث عن: "جنيه ذهب 37400 ج.م بيع"
            if (preg_match('/جنيه\s*ذهب\s*([0-9,]+\.?[0-9]*)\s*ج\.م\s*بيع/u', $text, $match)) {
                $price = floatval(str_replace(',', '', $match[1]));
                if ($price > 10000) {
                    $foundPrices[] = [
                        'purity_text' => 'جنيه ذهب',
                        'price' => $price,
                        'context' => 'جنيه ذهب ' . $price . ' ج.م بيع'
                    ];
                }
            }

            // Pattern 8: البحث العام عن أي ذكر للجنيه الذهبي مع سعر
            if (preg_match_all('/جنيه[^0-9]*([0-9,]+\.?[0-9]*)/u', $text, $matches8, PREG_SET_ORDER)) {
                foreach ($matches8 as $match) {
                    $price = floatval(str_replace(',', '', $match[1]));
                    if ($price > 30000 && $price < 50000) { // نطاق معقول لسعر الجنيه الذهبي
                        $foundPrices[] = [
                            'purity_text' => 'جنيه ذهب',
                            'price' => $price,
                            'context' => 'جنيه ذهب - ' . $price
                        ];
                    }
                }
            }

            // Pattern 9: إزالة التصنيف التلقائي للجنيهات الذهبية لتجنب التكرار
            // سنعتمد فقط على البحث المحدد مع كلمة "بيع"

            // Pattern 10: البحث المحدد عن عيارات الفضة المفقودة (مع التأكد من السياق)
            $specificSilverPrices = [
                '59' => '999',
                '54.75' => '925',
                '53.25' => '900'
            ];

            foreach ($specificSilverPrices as $priceStr => $purity) {
                // البحث مع التأكد من وجود كلمة "بيع" أو سياق الفضة
                if (preg_match('/' . preg_quote($priceStr) . '\s*ج\.م\s*بيع/u', $text) ||
                    preg_match('/فضة.*' . preg_quote($priceStr) . '/u', $text) ||
                    preg_match('/' . $purity . '.*' . preg_quote($priceStr) . '\s*ج\.م\s*بيع/u', $text)) {
                    $foundPrices[] = [
                        'purity_text' => 'فضة عيار ' . $purity,
                        'price' => floatval($priceStr),
                        'context' => 'فضة عيار ' . $purity . ' - ' . $priceStr . ' ج.م بيع (بحث محدد)'
                    ];
                }
            }

            // Pattern 11: البحث المحدد للجنيه الذهبي (للتشخيص فقط)
            // البحث عن الجنيه الذهبي مع كلمة "بيع"
            if (preg_match('/37[,]?400\s*ج\.م\s*بيع/u', $text) ||
                preg_match('/جنيه\s*ذهب.*37[,]?400.*بيع/u', $text)) {
                $foundPrices[] = [
                    'purity_text' => 'جنيه ذهب',
                    'price' => 37400,
                    'context' => 'جنيه ذهب - 37400 ج.م بيع (بحث محدد)'
                ];
            }

            // لا نحسب نصف وربع الجنيه - جلب الجنيه الكامل فقط

            // لا نحتاج فلترة الجنيهات الذهبية لأننا لا نجلبها

            // التشخيص يعرض جميع الأسعار بما فيها الجنيه الذهبي

            // إزالة التكرارات مع إعطاء الأولوية لأسعار البيع
            $uniquePrices = [];
            foreach ($foundPrices as $item) {
                $key = $item['purity_text'];

                // إعطاء الأولوية لأسعار البيع
                if (!isset($uniquePrices[$key])) {
                    $uniquePrices[$key] = $item;
                } else {
                    // إذا كان السعر الجديد يحتوي على "بيع" والموجود لا يحتوي
                    $newHasSell = strpos($item['context'], 'بيع') !== false;
                    $existingHasSell = strpos($uniquePrices[$key]['context'], 'بيع') !== false;

                    if ($newHasSell && !$existingHasSell) {
                        // استبدال بسعر البيع
                        $uniquePrices[$key] = $item;
                    } elseif ($newHasSell && $existingHasSell && $item['price'] > $uniquePrices[$key]['price']) {
                        // إذا كان كلاهما سعر بيع، اختر الأعلى
                        $uniquePrices[$key] = $item;
                    } elseif (!$newHasSell && !$existingHasSell && $item['price'] > $uniquePrices[$key]['price']) {
                        // إذا لم يكن أي منهما سعر بيع، اختر الأعلى
                        $uniquePrices[$key] = $item;
                    }
                }
            }

            return array_values($uniquePrices);

        } catch (\Exception $e) {
            Log::error('Error extracting prices from HTML', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * البحث عن سعر عيار معين في الأسعار المستخرجة
     */
    private static function findPriceForPurity($allFoundPrices, $purityName, $purityNameAr, $metalType)
    {
        // إنشاء قائمة بالكلمات المفتاحية للبحث
        $searchTerms = [];

        // إضافة اسم العيار
        $cleanPurity = str_replace(['K', 'k'], '', $purityName);
        $searchTerms[] = $cleanPurity;
        $searchTerms[] = $purityName;
        $searchTerms[] = $purityNameAr;

        // إضافة كلمات مفتاحية حسب نوع المعدن (للتشخيص - يشمل الجنيه الذهبي)
        $metalKeywords = [
            'gold' => ['ذهب', 'الذهب', 'gold'],
            'silver' => ['فضة', 'الفضة', 'silver'],
            'gold_coin' => ['جنيه ذهب', 'جنيه', 'الجنيه'],
        ];

        $keywords = $metalKeywords[$metalType] ?? [];

        foreach ($allFoundPrices as $priceItem) {
            $purityText = $priceItem['purity_text'];

            // البحث عن العيار في النص
            $foundPurity = false;
            foreach ($searchTerms as $term) {
                if (stripos($purityText, $term) !== false) {
                    $foundPurity = true;
                    break;
                }
            }

            // البحث عن نوع المعدن في النص
            $foundMetal = empty($keywords); // إذا لم تكن هناك كلمات مفتاحية، اعتبر أنه وُجد
            foreach ($keywords as $keyword) {
                if (stripos($purityText, $keyword) !== false) {
                    $foundMetal = true;
                    break;
                }
            }

            if ($foundPurity && $foundMetal) {
                Log::info("Found matching price", [
                    'purity' => $purityName,
                    'metal' => $metalType,
                    'price' => $priceItem['price'],
                    'context' => $priceItem['purity_text']
                ]);
                return $priceItem['price'];
            } else {
                Log::info("Price not matched", [
                    'purity' => $purityName,
                    'metal' => $metalType,
                    'found_purity' => $foundPurity,
                    'found_metal' => $foundMetal,
                    'search_terms' => $searchTerms,
                    'keywords' => $keywords,
                    'price_text' => $priceItem['purity_text']
                ]);
            }
        }

        return null;
    }

    /**
     * العثور على العيارات المفقودة في النظام
     */
    private static function findMissingPurities($allFoundPrices, $metalTypes)
    {
        $missingPurities = [];
        $existingPurities = [];

        // جمع جميع العيارات الموجودة في النظام
        foreach ($metalTypes as $metalType) {
            foreach ($metalType->purities as $purity) {
                $existingPurities[] = [
                    'metal_type' => $metalType->name,
                    'purity_name' => $purity->name,
                    'purity_name_ar' => $purity->name_ar,
                ];
            }
        }

        // تحليل الأسعار المستخرجة للعثور على عيارات جديدة
        foreach ($allFoundPrices as $priceItem) {
            $purityText = $priceItem['purity_text'];
            $price = $priceItem['price'];

            // تحديد نوع المعدن من النص
            $detectedMetal = static::detectMetalType($purityText);
            if (!$detectedMetal) continue;

            // استخراج العيار من النص
            $detectedPurity = static::extractPurityFromText($purityText);
            if (!$detectedPurity) continue;

            // التحقق من وجود هذا العيار في النظام
            $exists = false;
            foreach ($existingPurities as $existing) {
                if ($existing['metal_type'] === $detectedMetal &&
                    (stripos($detectedPurity, $existing['purity_name']) !== false ||
                     stripos($detectedPurity, $existing['purity_name_ar']) !== false)) {
                    $exists = true;
                    break;
                }
            }

            if (!$exists && static::isPriceReasonable($detectedMetal, $price)) {
                $key = $detectedMetal . '_' . $detectedPurity;
                if (!isset($missingPurities[$key])) {
                    $missingPurities[$key] = [
                        'metal_type' => $detectedMetal,
                        'detected_purity' => $detectedPurity,
                        'price' => $price,
                        'context' => $purityText,
                        'suggested_name' => static::suggestPurityName($detectedPurity),
                        'suggested_name_ar' => static::suggestPurityNameAr($detectedPurity),
                    ];
                }
            }
        }

        return array_values($missingPurities);
    }

    /**
     * تحديد نوع المعدن من النص (للتشخيص - يشمل الجنيه الذهبي)
     */
    private static function detectMetalType($text)
    {
        if (stripos($text, 'ذهب') !== false || stripos($text, 'gold') !== false) {
            if (stripos($text, 'جنيه') !== false) {
                return 'gold_coin';
            }
            return 'gold';
        }

        if (stripos($text, 'فضة') !== false || stripos($text, 'silver') !== false) {
            return 'silver';
        }

        return null;
    }

    /**
     * استخراج العيار من النص
     */
    private static function extractPurityFromText($text)
    {
        // البحث عن أرقام العيارات
        if (preg_match('/(\d+)\s*(?:قيراط|K|k|كيلو)/i', $text, $matches)) {
            return $matches[1] . 'K';
        }

        if (preg_match('/(\d+)/', $text, $matches)) {
            $number = $matches[1];
            // إذا كان الرقم كبير، فهو على الأرجح عيار فضة
            if ($number >= 600) {
                return $number;
            }
            // إذا كان صغير، فهو على الأرجح عيار ذهب
            if ($number <= 24) {
                return $number . 'K';
            }
        }

        return null;
    }

    /**
     * اقتراح اسم العيار بالإنجليزية
     */
    private static function suggestPurityName($detectedPurity)
    {
        return $detectedPurity;
    }

    /**
     * اقتراح اسم العيار بالعربية
     */
    private static function suggestPurityNameAr($detectedPurity)
    {
        if (strpos($detectedPurity, 'K') !== false) {
            $number = str_replace('K', '', $detectedPurity);
            return $number . ' قيراط';
        }

        return 'عيار ' . $detectedPurity;
    }
}
