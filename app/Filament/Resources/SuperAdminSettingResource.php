<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SuperAdminSettingResource\Pages;
use App\Models\SuperAdminSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Select;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Illuminate\Support\Facades\Auth;
use App\Traits\HasPermissionFiltering;

class SuperAdminSettingResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = SuperAdminSetting::class;
    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    protected static ?string $navigationLabel = 'إعدادات السوبر أدمن';
    protected static ?string $navigationGroup = 'إعدادات النظام المتقدمة';
    protected static ?int $navigationSort = 1;

    public static function getModelLabel(): string
    {
        return __('إعدادات السوبر أدمن');
    }

    public static function getPluralModelLabel(): string
    {
        return __('إعدادات السوبر أدمن');
    }

    public static function canViewAny(): bool
    {
        return Auth::user()?->hasAnyRole(['super_admin', 'admin']) ?? false;
    }

    public static function canCreate(): bool
    {
        return Auth::user()?->hasRole('super_admin') ?? false;
    }

    public static function canEdit($record): bool
    {
        return Auth::user()?->hasAnyRole(['super_admin', 'admin']) ?? false;
    }

    public static function canDelete($record): bool
    {
        return Auth::user()?->hasRole('super_admin') ?? false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('إعدادات السوبر أدمن')
                    ->tabs([
                        // تبويب ميزات الموقع
                        Tabs\Tab::make('ميزات الموقع')
                            ->icon('heroicon-o-star')
                            ->schema([
                                Section::make('ميزات العرض والتفاعل')
                                    ->description('التحكم في الميزات المتاحة للمستخدمين في الموقع')
                                    ->schema([
                                        Toggle::make('show_ratings')
                                            ->label('عرض تقييمات النجوم')
                                            ->helperText('عرض تقييمات النجوم للمنتجات في جميع صفحات الموقع')
                                            ->default(true),
                                        Toggle::make('show_wishlist')
                                            ->label('عرض المفضلة')
                                            ->helperText('عرض المفضلة وكل ما يتعلق بها في جميع صفحات الموقع')
                                            ->default(true),
                                        Toggle::make('enable_guest_checkout')
                                            ->label('تفعيل الشراء كزائر')
                                            ->helperText('السماح للزوار بإتمام الشراء دون تسجيل حساب')
                                            ->default(true),
                                        Toggle::make('enable_local_pickup')
                                            ->label('تفعيل الاستلام من المتجر')
                                            ->helperText('السماح للعملاء باستلام الطلبات من المتجر مباشرة')
                                            ->default(false),
                                        Toggle::make('enable_multilingual')
                                            ->label('تفعيل تعدد اللغات')
                                            ->helperText('تفعيل تعدد اللغات في الموقع')
                                            ->default(true),
                                    ])->columns(2),
                            ]),

                        // تبويب الضرائب والأسعار
                        Tabs\Tab::make('الضرائب والأسعار')
                            ->icon('heroicon-o-currency-dollar')
                            ->schema([
                                Section::make('إعدادات الضرائب والأسعار')
                                    ->description('إعدادات الضرائب وعرض الأسعار والطلبات')
                                    ->schema([
                                        TextInput::make('tax_rate')
                                            ->label('نسبة الضريبة')
                                            ->numeric()
                                            ->suffix('%')
                                            ->step(0.01)
                                            ->helperText('نسبة الضريبة المضافة على المنتجات')
                                            ->default(14.00),
                                        Toggle::make('prices_include_tax')
                                            ->label('الأسعار تشمل الضريبة')
                                            ->helperText('هل الأسعار المعروضة تشمل الضريبة؟')
                                            ->default(true),
                                        TextInput::make('min_order_amount')
                                            ->label('الحد الأدنى لقيمة الطلب')
                                            ->helperText('الحد الأدنى لقيمة الطلب للتمكن من إتمام الشراء')
                                            ->numeric()
                                            ->prefix('ج.م')
                                            ->step(0.01)
                                            ->default(0),
                                        TextInput::make('order_prefix')
                                            ->label('بادئة رقم الطلب')
                                            ->helperText('بادئة تضاف إلى رقم الطلب، مثل: MGJ-')
                                            ->maxLength(10)
                                            ->placeholder('MGJ-')
                                            ->default('MGJ-'),
                                    ])->columns(2),
                            ]),
                        // تبويب التسجيل والحسابات
                        Tabs\Tab::make('التسجيل والحسابات')
                            ->icon('heroicon-o-user-plus')
                            ->schema([
                                Section::make('إعدادات التسجيل')
                                    ->description('التحكم في تسجيل المستخدمين الجدد')
                                    ->schema([
                                        Toggle::make('enable_registration')
                                            ->label('تفعيل التسجيل في الموقع')
                                            ->helperText('السماح للزوار بالتسجيل في الموقع')
                                            ->live()
                                            ->default(true),
                                        Textarea::make('registration_disabled_message')
                                            ->label('رسالة تعطيل التسجيل')
                                            ->helperText('الرسالة التي ستظهر للزوار عند تعطيل التسجيل')
                                            ->rows(3)
                                            ->placeholder('التسجيل غير متاح حالياً، يرجى المحاولة لاحقاً.')
                                            ->visible(fn ($get) => !$get('enable_registration')),
                                    ])->columns(1),
                            ]),

                        // تبويب إعدادات العملات
                        Tabs\Tab::make('إعدادات العملات')
                            ->icon('heroicon-o-banknotes')
                            ->schema([
                                Section::make('إعدادات العملات')
                                    ->description('إعدادات العملات المدعومة وتحويل العملات')
                                    ->schema([
                                        Select::make('default_currency')
                                            ->label('العملة الافتراضية')
                                            ->options([
                                                'EGP' => 'جنيه مصري (EGP)',
                                                'USD' => 'دولار أمريكي (USD)',
                                                'EUR' => 'يورو (EUR)',
                                                'SAR' => 'ريال سعودي (SAR)',
                                                'AED' => 'درهم إماراتي (AED)',
                                            ])
                                            ->default('EGP')
                                            ->helperText('العملة الافتراضية للموقع'),
                                        Select::make('supported_currencies')
                                            ->label('العملات المدعومة')
                                            ->multiple()
                                            ->options([
                                                'EGP' => 'جنيه مصري (EGP)',
                                                'USD' => 'دولار أمريكي (USD)',
                                                'EUR' => 'يورو (EUR)',
                                                'SAR' => 'ريال سعودي (SAR)',
                                                'AED' => 'درهم إماراتي (AED)',
                                            ])
                                            ->default(['EGP', 'USD', 'EUR'])
                                            ->helperText('العملات المتاحة للمستخدمين للاختيار من بينها'),
                                        TextInput::make('currency_api_key')
                                            ->label('مفتاح API لتحويل العملات')
                                            ->helperText('مفتاح API لخدمة تحويل العملات (اختياري)')
                                            ->placeholder('أدخل مفتاح API'),
                                    ])->columns(1),
                            ]),

                        // تبويب حالة الموقع والصيانة
                        Tabs\Tab::make('حالة الموقع والصيانة')
                            ->icon('heroicon-o-wrench-screwdriver')
                            ->schema([
                                Section::make('إعدادات الصيانة')
                                    ->description('التحكم في حالة الموقع ووضع الصيانة')
                                    ->schema([
                                        Toggle::make('maintenance_mode')
                                            ->label('وضع الصيانة')
                                            ->helperText('تفعيل وضع الصيانة لمنع الوصول للموقع')
                                            ->live()
                                            ->default(false),
                                        Textarea::make('maintenance_message')
                                            ->label('رسالة الصيانة')
                                            ->helperText('الرسالة التي ستظهر للزوار أثناء الصيانة')
                                            ->rows(3)
                                            ->placeholder('الموقع قيد الصيانة حالياً، يرجى العودة لاحقاً.')
                                            ->visible(fn ($get) => $get('maintenance_mode')),
                                        Toggle::make('display_only_mode')
                                            ->label('وضع العرض فقط')
                                            ->helperText('تفعيل وضع العرض فقط (منع الشراء والطلبات)')
                                            ->default(false),
                                    ])->columns(1),
                            ]),

                        // تبويب إعدادات اللغة والترجمة
                        Tabs\Tab::make('إعدادات اللغة والترجمة')
                            ->icon('heroicon-o-language')
                            ->schema([
                                Section::make('إعدادات اللغة والترجمة')
                                    ->description('إعدادات اللغة الافتراضية والترجمة')
                                    ->schema([
                                        Select::make('default_language')
                                            ->label('اللغة الافتراضية')
                                            ->options([
                                                'ar' => 'العربية',
                                                'en' => 'English',
                                            ])
                                            ->default('ar')
                                            ->helperText('اللغة الافتراضية للموقع'),
                                        Forms\Components\Actions::make([
                                            Forms\Components\Actions\Action::make('edit_translations')
                                                ->label('تعديل الترجمات')
                                                ->url(fn () => route('filament.admin.pages.translations-manager'))
                                                ->icon('heroicon-o-language')
                                                ->color('primary'),
                                            Forms\Components\Actions\Action::make('manage_languages')
                                                ->label('إدارة اللغات المتاحة')
                                                ->url(fn () => route('filament.admin.resources.language-managers.index'))
                                                ->icon('heroicon-o-globe-alt')
                                                ->color('success'),
                                        ]),
                                    ])->columns(2),
                            ]),

                        // تبويب إعدادات الدفع
                        Tabs\Tab::make('إعدادات الدفع')
                            ->icon('heroicon-o-credit-card')
                            ->schema([
                                Section::make('طرق الدفع المتاحة')
                                    ->description('تفعيل وتعطيل طرق الدفع المختلفة')
                                    ->schema([
                                        Toggle::make('enable_credit_card')
                                            ->label('تفعيل الدفع بالبطاقة الائتمانية')
                                            ->helperText('تفعيل الدفع بالبطاقات الائتمانية عبر Stripe')
                                            ->default(true),
                                        Toggle::make('enable_paypal')
                                            ->label('تفعيل الدفع عبر PayPal')
                                            ->helperText('تفعيل الدفع عبر PayPal')
                                            ->default(true),
                                        Toggle::make('enable_bank_transfer')
                                            ->label('تفعيل التحويل البنكي')
                                            ->helperText('تفعيل الدفع عبر التحويل البنكي')
                                            ->default(false),
                                        Toggle::make('enable_cash_on_delivery')
                                            ->label('تفعيل الدفع عند الاستلام')
                                            ->helperText('تفعيل الدفع النقدي عند استلام الطلب')
                                            ->default(false),
                                        Toggle::make('enable_fawry')
                                            ->label('تفعيل الدفع عبر فوري')
                                            ->helperText('تفعيل الدفع عبر خدمة فوري')
                                            ->default(false),
                                    ])->columns(2),

                                Section::make('إعدادات Stripe')
                                    ->description('إعدادات بوابة الدفع Stripe')
                                    ->schema([
                                        TextInput::make('stripe_key')
                                            ->label('مفتاح Stripe العام')
                                            ->helperText('المفتاح العام لـ Stripe (Publishable Key)')
                                            ->placeholder('pk_test_...'),
                                        TextInput::make('stripe_secret')
                                            ->label('مفتاح Stripe السري')
                                            ->helperText('المفتاح السري لـ Stripe (Secret Key)')
                                            ->password()
                                            ->placeholder('sk_test_...'),
                                        Toggle::make('stripe_sandbox_mode')
                                            ->label('وضع الاختبار لـ Stripe')
                                            ->helperText('تفعيل وضع الاختبار (Sandbox Mode)')
                                            ->default(true),
                                    ])->columns(1),

                                Section::make('إعدادات PayPal')
                                    ->description('إعدادات بوابة الدفع PayPal')
                                    ->schema([
                                        TextInput::make('paypal_client_id')
                                            ->label('معرف عميل PayPal')
                                            ->helperText('معرف العميل الخاص بـ PayPal')
                                            ->placeholder('AXxxx...'),
                                        TextInput::make('paypal_secret')
                                            ->label('مفتاح PayPal السري')
                                            ->helperText('المفتاح السري لـ PayPal')
                                            ->password()
                                            ->placeholder('ELxxx...'),
                                        Toggle::make('paypal_sandbox_mode')
                                            ->label('وضع الاختبار لـ PayPal')
                                            ->helperText('تفعيل وضع الاختبار (Sandbox Mode)')
                                            ->default(true),
                                    ])->columns(1),
                            ]),

                        // تبويب إعدادات الشحن والفواتير
                        Tabs\Tab::make('الشحن والفواتير')
                            ->icon('heroicon-o-truck')
                            ->schema([
                                Section::make('إعدادات الشحن المتقدمة')
                                    ->description('إعدادات الشحن والاستلام')
                                    ->schema([
                                        TextInput::make('local_pickup_discount')
                                            ->label('خصم الاستلام من المتجر')
                                            ->helperText('مبلغ الخصم عند اختيار الاستلام من المتجر')
                                            ->numeric()
                                            ->prefix('ج.م')
                                            ->step(0.01)
                                            ->default(0),
                                        Textarea::make('shipping_policy')
                                            ->label('سياسة الشحن')
                                            ->helperText('سياسة الشحن المختصرة التي تظهر في صفحة الدفع')
                                            ->rows(3)
                                            ->placeholder('سياسة الشحن والتوصيل...'),
                                    ])->columns(1),

                                Section::make('إعدادات الفواتير')
                                    ->description('إعدادات إنشاء وطباعة الفواتير')
                                    ->schema([
                                        Toggle::make('enable_invoices')
                                            ->label('تفعيل الفواتير')
                                            ->helperText('تفعيل إنشاء وطباعة الفواتير للطلبات')
                                            ->live()
                                            ->default(true),
                                        TextInput::make('invoice_prefix')
                                            ->label('بادئة رقم الفاتورة')
                                            ->helperText('بادئة تضاف إلى رقم الفاتورة، مثل: INV-')
                                            ->maxLength(10)
                                            ->placeholder('INV-')
                                            ->visible(fn ($get) => $get('enable_invoices')),
                                        TextInput::make('company_name_invoice')
                                            ->label('اسم الشركة في الفاتورة')
                                            ->helperText('اسم الشركة الذي يظهر في الفاتورة')
                                            ->placeholder('مكة الذهب للمجوهرات')
                                            ->visible(fn ($get) => $get('enable_invoices')),
                                        Textarea::make('company_address_invoice')
                                            ->label('عنوان الشركة في الفاتورة')
                                            ->helperText('عنوان الشركة الذي يظهر في الفاتورة')
                                            ->rows(3)
                                            ->placeholder('العنوان الكامل للشركة...')
                                            ->visible(fn ($get) => $get('enable_invoices')),
                                        TextInput::make('company_tax_id')
                                            ->label('الرقم الضريبي للشركة')
                                            ->helperText('الرقم الضريبي الذي يظهر في الفاتورة')
                                            ->placeholder('*********')
                                            ->visible(fn ($get) => $get('enable_invoices')),
                                    ])->columns(1),
                            ]),

                        // تبويب تكامل وسائل التواصل الاجتماعي
                        Tabs\Tab::make('تكامل وسائل التواصل الاجتماعي')
                            ->icon('heroicon-o-share')
                            ->schema([
                                Section::make('تسجيل الدخول بوسائل التواصل الاجتماعي')
                                    ->description('إعدادات تسجيل الدخول عبر منصات التواصل الاجتماعي')
                                    ->schema([
                                        Toggle::make('enable_social_login')
                                            ->label('تفعيل تسجيل الدخول بوسائل التواصل الاجتماعي')
                                            ->helperText('تفعيل تسجيل الدخول عبر منصات التواصل الاجتماعي')
                                            ->live()
                                            ->default(false),
                                        Toggle::make('enable_facebook_login')
                                            ->label('تفعيل تسجيل الدخول بفيسبوك')
                                            ->helperText('السماح للمستخدمين بتسجيل الدخول عبر فيسبوك')
                                            ->visible(fn ($get) => $get('enable_social_login'))
                                            ->default(false),
                                        Toggle::make('enable_google_login')
                                            ->label('تفعيل تسجيل الدخول بجوجل')
                                            ->helperText('السماح للمستخدمين بتسجيل الدخول عبر جوجل')
                                            ->visible(fn ($get) => $get('enable_social_login'))
                                            ->default(false),
                                        Toggle::make('enable_twitter_login')
                                            ->label('تفعيل تسجيل الدخول بتويتر')
                                            ->helperText('السماح للمستخدمين بتسجيل الدخول عبر تويتر')
                                            ->visible(fn ($get) => $get('enable_social_login'))
                                            ->default(false),
                                    ])->columns(2),

                                Section::make('مفاتيح API لتسجيل الدخول')
                                    ->description('مفاتيح API المطلوبة لتسجيل الدخول عبر منصات التواصل الاجتماعي')
                                    ->schema([
                                        TextInput::make('facebook_app_id')
                                            ->label('معرف تطبيق فيسبوك')
                                            ->helperText('معرف التطبيق من Facebook Developers')
                                            ->placeholder('*********012345')
                                            ->visible(fn ($get) => $get('enable_facebook_login')),
                                        TextInput::make('facebook_app_secret')
                                            ->label('سر تطبيق فيسبوك')
                                            ->helperText('سر التطبيق من Facebook Developers')
                                            ->password()
                                            ->placeholder('xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx')
                                            ->visible(fn ($get) => $get('enable_facebook_login')),
                                        TextInput::make('google_client_id')
                                            ->label('معرف عميل جوجل')
                                            ->helperText('معرف العميل من Google Cloud Console')
                                            ->placeholder('*********012-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.apps.googleusercontent.com')
                                            ->visible(fn ($get) => $get('enable_google_login')),
                                        TextInput::make('google_client_secret')
                                            ->label('سر عميل جوجل')
                                            ->helperText('سر العميل من Google Cloud Console')
                                            ->password()
                                            ->placeholder('GOCSPX-xxxxxxxxxxxxxxxxxxxxxxxx')
                                            ->visible(fn ($get) => $get('enable_google_login')),
                                        TextInput::make('twitter_client_id')
                                            ->label('معرف عميل تويتر')
                                            ->helperText('معرف العميل من Twitter Developer Portal')
                                            ->placeholder('xxxxxxxxxxxxxxxxxxxxxxxx')
                                            ->visible(fn ($get) => $get('enable_twitter_login')),
                                        TextInput::make('twitter_client_secret')
                                            ->label('سر عميل تويتر')
                                            ->helperText('سر العميل من Twitter Developer Portal')
                                            ->password()
                                            ->placeholder('xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx')
                                            ->visible(fn ($get) => $get('enable_twitter_login')),
                                    ])->columns(1),

                                Section::make('مشاركة المنتجات')
                                    ->description('إعدادات مشاركة المنتجات على منصات التواصل الاجتماعي')
                                    ->schema([
                                        Toggle::make('enable_social_sharing')
                                            ->label('تفعيل مشاركة المنتجات')
                                            ->helperText('تفعيل أزرار مشاركة المنتجات على منصات التواصل الاجتماعي')
                                            ->live()
                                            ->default(true),
                                        Toggle::make('share_on_facebook')
                                            ->label('مشاركة على فيسبوك')
                                            ->helperText('إظهار زر مشاركة المنتجات على فيسبوك')
                                            ->visible(fn ($get) => $get('enable_social_sharing'))
                                            ->default(true),
                                        Toggle::make('share_on_twitter')
                                            ->label('مشاركة على تويتر')
                                            ->helperText('إظهار زر مشاركة المنتجات على تويتر')
                                            ->visible(fn ($get) => $get('enable_social_sharing'))
                                            ->default(true),
                                        Toggle::make('share_on_whatsapp')
                                            ->label('مشاركة على واتساب')
                                            ->helperText('إظهار زر مشاركة المنتجات على واتساب')
                                            ->visible(fn ($get) => $get('enable_social_sharing'))
                                            ->default(true),
                                        Toggle::make('share_on_pinterest')
                                            ->label('مشاركة على بينتيريست')
                                            ->helperText('إظهار زر مشاركة المنتجات على بينتيريست')
                                            ->visible(fn ($get) => $get('enable_social_sharing'))
                                            ->default(false),
                                        Toggle::make('share_on_linkedin')
                                            ->label('مشاركة على لينكد إن')
                                            ->helperText('إظهار زر مشاركة المنتجات على لينكد إن')
                                            ->visible(fn ($get) => $get('enable_social_sharing'))
                                            ->default(false),
                                    ])->columns(3),

                                Section::make('Feeds وسائل التواصل الاجتماعي')
                                    ->description('إعدادات عرض منشورات وسائل التواصل الاجتماعي في الموقع')
                                    ->schema([
                                        Toggle::make('show_instagram_feed')
                                            ->label('عرض منشورات إنستغرام')
                                            ->helperText('عرض آخر منشورات إنستغرام في الموقع')
                                            ->live()
                                            ->default(false),
                                        TextInput::make('instagram_token')
                                            ->label('رمز إنستغرام المميز')
                                            ->helperText('رمز الوصول لـ Instagram Basic Display API')
                                            ->password()
                                            ->placeholder('IGQVJxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx')
                                            ->visible(fn ($get) => $get('show_instagram_feed')),
                                        TextInput::make('instagram_count')
                                            ->label('عدد منشورات إنستغرام')
                                            ->helperText('عدد المنشورات المراد عرضها من إنستغرام')
                                            ->numeric()
                                            ->minValue(1)
                                            ->maxValue(20)
                                            ->default(6)
                                            ->visible(fn ($get) => $get('show_instagram_feed')),
                                        Toggle::make('show_facebook_feed')
                                            ->label('عرض منشورات فيسبوك')
                                            ->helperText('عرض آخر منشورات صفحة فيسبوك في الموقع')
                                            ->live()
                                            ->default(false),
                                        TextInput::make('facebook_page_id')
                                            ->label('معرف صفحة فيسبوك')
                                            ->helperText('معرف صفحة فيسبوك المراد عرض منشوراتها')
                                            ->placeholder('*********012345')
                                            ->visible(fn ($get) => $get('show_facebook_feed')),
                                        TextInput::make('facebook_count')
                                            ->label('عدد منشورات فيسبوك')
                                            ->helperText('عدد المنشورات المراد عرضها من فيسبوك')
                                            ->numeric()
                                            ->minValue(1)
                                            ->maxValue(20)
                                            ->default(5)
                                            ->visible(fn ($get) => $get('show_facebook_feed')),
                                    ])->columns(2),
                            ]),

                        // تبويب ملفات تعريف الارتباط والخصوصية
                        Tabs\Tab::make('ملفات تعريف الارتباط والخصوصية')
                            ->icon('heroicon-o-shield-check')
                            ->schema([
                                Section::make('إعدادات ملفات تعريف الارتباط')
                                    ->description('إعدادات شريط ملفات تعريف الارتباط (Cookies)')
                                    ->schema([
                                        Toggle::make('show_cookie_banner')
                                            ->label('عرض شريط ملفات تعريف الارتباط')
                                            ->helperText('عرض شريط إشعار ملفات تعريف الارتباط للزوار')
                                            ->live()
                                            ->default(true),
                                        Textarea::make('cookie_banner_text')
                                            ->label('نص شريط ملفات تعريف الارتباط')
                                            ->helperText('النص الذي سيظهر في شريط ملفات تعريف الارتباط')
                                            ->rows(3)
                                            ->placeholder('هذا الموقع يستخدم ملفات تعريف الارتباط لتحسين تجربتك. بالاستمرار في استخدام هذا الموقع، فإنك توافق على استخدامنا لملفات تعريف الارتباط.')
                                            ->visible(fn ($get) => $get('show_cookie_banner')),
                                        TextInput::make('cookie_banner_button_text')
                                            ->label('نص زر الموافقة')
                                            ->helperText('نص الزر الذي يوافق على ملفات تعريف الارتباط')
                                            ->placeholder('أوافق')
                                            ->maxLength(50)
                                            ->visible(fn ($get) => $get('show_cookie_banner')),
                                    ])->columns(1),

                                Section::make('إعدادات GDPR')
                                    ->description('إعدادات الامتثال للائحة العامة لحماية البيانات (GDPR)')
                                    ->schema([
                                        Toggle::make('enable_gdpr_compliance')
                                            ->label('تفعيل الامتثال لـ GDPR')
                                            ->helperText('تفعيل ميزات الامتثال للائحة العامة لحماية البيانات')
                                            ->live()
                                            ->default(false),
                                        Textarea::make('gdpr_compliance_text')
                                            ->label('نص الامتثال لـ GDPR')
                                            ->helperText('النص الذي يوضح التزام الموقع بحماية البيانات')
                                            ->rows(3)
                                            ->placeholder('نحن نحترم خصوصيتك ونلتزم بحماية بياناتك الشخصية وفقاً للائحة العامة لحماية البيانات (GDPR).')
                                            ->visible(fn ($get) => $get('enable_gdpr_compliance')),
                                    ])->columns(1),

                                Section::make('موافقة التسويق')
                                    ->description('إعدادات طلب موافقة المستخدمين على الرسائل التسويقية')
                                    ->schema([
                                        Toggle::make('require_marketing_consent')
                                            ->label('طلب موافقة التسويق')
                                            ->helperText('طلب موافقة صريحة من المستخدمين لتلقي الرسائل التسويقية')
                                            ->live()
                                            ->default(false),
                                        Textarea::make('marketing_consent_text')
                                            ->label('نص موافقة التسويق')
                                            ->helperText('النص الذي يطلب موافقة المستخدم على الرسائل التسويقية')
                                            ->rows(3)
                                            ->placeholder('أوافق على تلقي رسائل تسويقية من مكة جولد عبر البريد الإلكتروني والرسائل القصيرة.')
                                            ->visible(fn ($get) => $get('require_marketing_consent')),
                                    ])->columns(1),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('المعرف')
                    ->sortable(),
                IconColumn::make('enable_registration')
                    ->label('التسجيل مفعل')
                    ->boolean(),
                IconColumn::make('show_ratings')
                    ->label('التقييمات')
                    ->boolean(),
                IconColumn::make('show_wishlist')
                    ->label('المفضلة')
                    ->boolean(),
                TextColumn::make('default_currency')
                    ->label('العملة الافتراضية'),
                TextColumn::make('updated_at')
                    ->label('آخر تحديث')
                    ->dateTime('d/m/Y H:i:s')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                // لا يوجد زر حذف لأنه لا يجب السماح بحذف إعدادات السوبر أدمن الحساسة
            ])
            ->bulkActions([
                // لا توجد أزرار حذف جماعي لأنه لا يجب السماح بحذف إعدادات السوبر أدمن
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageSuperAdminSettings::route('/'),
        ];
    }
}
