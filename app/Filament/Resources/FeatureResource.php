<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FeatureResource\Pages;
use App\Filament\Resources\FeatureResource\RelationManagers;
use App\Models\Feature;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\HasPermissionFiltering;
use Illuminate\Support\Collection;

class FeatureResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = Feature::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    protected static ?string $navigationLabel = 'ميزات الموقع';

    protected static ?string $navigationGroup = 'إدارة المحتوى والمنتجات';

    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات الميزة')
                    ->schema([
                        Forms\Components\TextInput::make('title_ar')
                            ->label('العنوان (عربي)')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('title_en')
                            ->label('العنوان (إنجليزي)')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Textarea::make('description_ar')
                            ->label('الوصف (عربي)')
                            ->required()
                            ->rows(3),

                        Forms\Components\Textarea::make('description_en')
                            ->label('الوصف (إنجليزي)')
                            ->required()
                            ->rows(3),
                    ])->columns(2),

                Forms\Components\Section::make('إعدادات الميزة')
                    ->schema([
                        Forms\Components\TextInput::make('icon')
                            ->label('أيقونة (Font Awesome)')
                            ->placeholder('مثال: fas fa-gem')
                            ->helperText('أدخل اسم أيقونة Font Awesome مثل: fas fa-gem, fas fa-truck')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('order')
                            ->label('الترتيب')
                            ->numeric()
                            ->default(1)
                            ->minValue(1)
                            ->required(),

                        Forms\Components\Toggle::make('is_active')
                            ->label('نشط')
                            ->default(true)
                            ->required(),
                    ])->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order')
                    ->label('الترتيب')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('الحالة')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('icon')
                    ->label('الأيقونة')
                    ->formatStateUsing(fn (string $state): string => '<i class="' . $state . ' text-primary-500"></i>')
                    ->html(),

                Tables\Columns\TextColumn::make('title_ar')
                    ->label('العنوان (عربي)')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('title_en')
                    ->label('العنوان (إنجليزي)')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('order')
            ->filters([
                Tables\Filters\SelectFilter::make('is_active')
                    ->label('الحالة')
                    ->options([
                        '1' => 'نشط',
                        '0' => 'غير نشط',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('تفعيل')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true]))
                        ->icon('heroicon-o-check-circle'),
                    Tables\Actions\BulkAction::make('تعطيل')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => false]))
                        ->icon('heroicon-o-x-circle'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFeatures::route('/'),
            'create' => Pages\CreateFeature::route('/create'),
            'edit' => Pages\EditFeature::route('/{record}/edit'),
        ];
    }
}
