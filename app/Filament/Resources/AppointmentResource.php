<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AppointmentResource\Pages;
use App\Filament\Resources\AppointmentResource\RelationManagers;
use App\Models\Appointment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Collection;

class AppointmentResource extends Resource
{
    protected static ?string $model = Appointment::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar';

    protected static ?string $navigationLabel = 'المواعيد';

    protected static ?string $modelLabel = 'موعد';

    protected static ?string $pluralModelLabel = 'المواعيد';

    protected static ?string $navigationGroup = 'البيانات';

    protected static ?int $navigationSort = 3;

    protected static ?string $recordTitleAttribute = 'service_type_ar';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات الموعد')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('العميل')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Select::make('store_id')
                            ->label('المتجر')
                            ->relationship('store', 'name_ar')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\DateTimePicker::make('appointment_date')
                            ->label('تاريخ ووقت الموعد')
                            ->required()
                            ->timezone('Africa/Cairo')
                            ->format('Y-m-d H:i')
                            ->minDate(now()),

                        Forms\Components\Select::make('status')
                            ->label('حالة الموعد')
                            ->options([
                                'pending' => 'قيد الانتظار',
                                'confirmed' => 'مؤكد',
                                'cancelled' => 'ملغي',
                                'completed' => 'مكتمل',
                            ])
                            ->default('pending')
                            ->required(),

                        Forms\Components\TextInput::make('service_type_ar')
                            ->label('نوع الخدمة بالعربية')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('service_type_en')
                            ->label('نوع الخدمة بالإنجليزية')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('confirmation_code')
                            ->label('رمز التأكيد')
                            ->maxLength(20),

                        Forms\Components\Toggle::make('reminder_sent')
                            ->label('تم إرسال التذكير')
                            ->default(false),
                    ])->columns(2),

                Forms\Components\Section::make('ملاحظات')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('ملاحظات')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('العميل')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('store.name_ar')
                    ->label('المتجر')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('appointment_date')
                    ->label('تاريخ ووقت الموعد')
                    ->dateTime('d-m-Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('حالة الموعد')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'pending' => 'قيد الانتظار',
                        'confirmed' => 'مؤكد',
                        'cancelled' => 'ملغي',
                        'completed' => 'مكتمل',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'warning',
                        'confirmed' => 'success',
                        'cancelled' => 'danger',
                        'completed' => 'info',
                        default => 'secondary',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('service_type_ar')
                    ->label('نوع الخدمة')
                    ->searchable(),

                Tables\Columns\IconColumn::make('reminder_sent')
                    ->label('تم إرسال التذكير')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('store_id')
                    ->label('المتجر')
                    ->relationship('store', 'name_ar')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('status')
                    ->label('حالة الموعد')
                    ->options([
                        'pending' => 'قيد الانتظار',
                        'confirmed' => 'مؤكد',
                        'cancelled' => 'ملغي',
                        'completed' => 'مكتمل',
                    ]),

                Tables\Filters\Filter::make('appointment_date')
                    ->form([
                        Forms\Components\DatePicker::make('appointment_from')
                            ->label('من تاريخ'),
                        Forms\Components\DatePicker::make('appointment_until')
                            ->label('إلى تاريخ'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['appointment_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('appointment_date', '>=', $date),
                            )
                            ->when(
                                $data['appointment_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('appointment_date', '<=', $date),
                            );
                    }),

                Tables\Filters\TernaryFilter::make('reminder_sent')
                    ->label('تم إرسال التذكير'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('تأكيد المواعيد')
                        ->action(fn (Collection $records) => $records->each->update(['status' => 'confirmed']))
                        ->icon('heroicon-o-check-circle'),
                    Tables\Actions\BulkAction::make('إلغاء المواعيد')
                        ->action(fn (Collection $records) => $records->each->update(['status' => 'cancelled']))
                        ->icon('heroicon-o-x-circle'),
                    Tables\Actions\BulkAction::make('تحديث حالة المواعيد إلى مكتملة')
                        ->action(fn (Collection $records) => $records->each->update(['status' => 'completed']))
                        ->icon('heroicon-o-check'),
                    Tables\Actions\BulkAction::make('تحديث حالة التذكير')
                        ->action(fn (Collection $records) => $records->each->update(['reminder_sent' => true]))
                        ->icon('heroicon-o-bell'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAppointments::route('/'),
            'create' => Pages\CreateAppointment::route('/create'),
            'edit' => Pages\EditAppointment::route('/{record}/edit'),
        ];
    }
}
