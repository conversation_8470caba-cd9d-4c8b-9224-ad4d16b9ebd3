<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PageResource\Pages;
use App\Filament\Resources\PageResource\RelationManagers;
use App\Models\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Collection;

class PageResource extends Resource
{
    protected static ?string $model = Page::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'إدارة المحتوى والمنتجات';

    protected static ?int $navigationSort = 3;

    public static function getNavigationLabel(): string
    {
        return __('الصفحات');
    }

    public static function getModelLabel(): string
    {
        return __('صفحة');
    }

    public static function getPluralModelLabel(): string
    {
        return __('الصفحات');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('الصفحة')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('المعلومات الأساسية')
                            ->schema([
                                Forms\Components\TextInput::make('title')
                                    ->label('العنوان')
                                    ->required()
                                    ->maxLength(255)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(fn (string $operation, $state, Forms\Set $set) =>
                                        $operation === 'create' ? $set('slug', \Illuminate\Support\Str::slug($state)) : null),

                                Forms\Components\TextInput::make('slug')
                                    ->label('المعرف الفريد (Slug)')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true),

                                Forms\Components\Select::make('type')
                                    ->label('نوع الصفحة')
                                    ->options([
                                        'page' => 'صفحة عادية',
                                        'faq' => 'الأسئلة الشائعة',
                                        'shipping' => 'سياسة الشحن',
                                        'returns' => 'سياسة الإرجاع',
                                        'privacy' => 'سياسة الخصوصية',
                                        'terms' => 'الشروط والأحكام',
                                    ])
                                    ->default('page')
                                    ->required(),

                                Forms\Components\Select::make('section')
                                    ->label('القسم')
                                    ->options([
                                        'customer-service' => 'خدمة العملاء',
                                        'legal' => 'القانونية',
                                        'about' => 'عن الشركة',
                                        'other' => 'أخرى',
                                    ])
                                    ->default('customer-service'),

                                Forms\Components\TextInput::make('order')
                                    ->label('الترتيب')
                                    ->numeric()
                                    ->default(0),

                                Forms\Components\Toggle::make('is_active')
                                    ->label('نشط')
                                    ->default(true),
                            ]),

                        Forms\Components\Tabs\Tab::make('المحتوى')
                            ->schema([
                                Forms\Components\RichEditor::make('content')
                                    ->label('المحتوى')
                                    ->required()
                                    ->fileAttachmentsDisk('public')
                                    ->fileAttachmentsDirectory('pages')
                                    ->toolbarButtons([
                                        'blockquote',
                                        'bold',
                                        'bulletList',
                                        'codeBlock',
                                        'h2',
                                        'h3',
                                        'italic',
                                        'link',
                                        'orderedList',
                                        'redo',
                                        'strike',
                                        'underline',
                                        'undo',
                                    ]),
                            ]),

                        Forms\Components\Tabs\Tab::make('SEO')
                            ->schema([
                                Forms\Components\TextInput::make('meta_title')
                                    ->label('عنوان الميتا')
                                    ->maxLength(255),

                                Forms\Components\Textarea::make('meta_description')
                                    ->label('وصف الميتا')
                                    ->rows(3),

                                Forms\Components\TextInput::make('meta_keywords')
                                    ->label('الكلمات المفتاحية')
                                    ->maxLength(255),
                            ]),

                        Forms\Components\Tabs\Tab::make('الترجمات')
                            ->schema([
                                Forms\Components\Section::make('الترجمة الإنجليزية')
                                    ->schema([
                                        Forms\Components\TextInput::make('translations.en.title')
                                            ->label('العنوان (الإنجليزية)')
                                            ->maxLength(255),

                                        Forms\Components\RichEditor::make('translations.en.content')
                                            ->label('المحتوى (الإنجليزية)')
                                            ->fileAttachmentsDisk('public')
                                            ->fileAttachmentsDirectory('pages')
                                            ->toolbarButtons([
                                                'blockquote',
                                                'bold',
                                                'bulletList',
                                                'codeBlock',
                                                'h2',
                                                'h3',
                                                'italic',
                                                'link',
                                                'orderedList',
                                                'redo',
                                                'strike',
                                                'underline',
                                                'undo',
                                            ]),

                                        Forms\Components\TextInput::make('translations.en.meta_title')
                                            ->label('عنوان الميتا (الإنجليزية)')
                                            ->maxLength(255),

                                        Forms\Components\Textarea::make('translations.en.meta_description')
                                            ->label('وصف الميتا (الإنجليزية)')
                                            ->rows(3),
                                    ]),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('العنوان')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('slug')
                    ->label('المعرف الفريد')
                    ->searchable(),

                Tables\Columns\TextColumn::make('type')
                    ->label('النوع')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'page' => 'صفحة عادية',
                        'faq' => 'الأسئلة الشائعة',
                        'shipping' => 'سياسة الشحن',
                        'returns' => 'سياسة الإرجاع',
                        'privacy' => 'سياسة الخصوصية',
                        'terms' => 'الشروط والأحكام',
                        default => $state,
                    })
                    ->colors([
                        'primary' => 'page',
                        'success' => 'faq',
                        'warning' => 'shipping',
                        'danger' => 'returns',
                        'info' => 'privacy',
                        'gray' => 'terms',
                    ]),

                Tables\Columns\TextColumn::make('section')
                    ->label('القسم')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'customer-service' => 'خدمة العملاء',
                        'legal' => 'القانونية',
                        'about' => 'عن الشركة',
                        'other' => 'أخرى',
                        default => $state,
                    })
                    ->colors([
                        'success' => 'customer-service',
                        'danger' => 'legal',
                        'warning' => 'about',
                        'gray' => 'other',
                    ]),

                Tables\Columns\TextColumn::make('order')
                    ->label('الترتيب')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('النوع')
                    ->options([
                        'page' => 'صفحة عادية',
                        'faq' => 'الأسئلة الشائعة',
                        'shipping' => 'سياسة الشحن',
                        'returns' => 'سياسة الإرجاع',
                        'privacy' => 'سياسة الخصوصية',
                        'terms' => 'الشروط والأحكام',
                    ]),

                Tables\Filters\SelectFilter::make('section')
                    ->label('القسم')
                    ->options([
                        'customer-service' => 'خدمة العملاء',
                        'legal' => 'القانونية',
                        'about' => 'عن الشركة',
                        'other' => 'أخرى',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('نشط'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('تفعيل')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true]))
                        ->requiresConfirmation()
                        ->color('success')
                        ->icon('heroicon-o-check'),
                    Tables\Actions\BulkAction::make('تعطيل')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => false]))
                        ->requiresConfirmation()
                        ->color('danger')
                        ->icon('heroicon-o-x-mark'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPages::route('/'),
            'create' => Pages\CreatePage::route('/create'),
            'edit' => Pages\EditPage::route('/{record}/edit'),
        ];
    }
}
