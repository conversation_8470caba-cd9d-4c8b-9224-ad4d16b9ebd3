<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Filament\Resources\ProductResource\RelationManagers;
use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Collection;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationLabel = 'المنتجات';

    protected static ?string $modelLabel = 'منتج';

    protected static ?string $pluralModelLabel = 'منتجات';

    protected static ?string $navigationGroup = 'المحتوى';

    protected static ?int $navigationSort = 1;

    protected static ?string $recordTitleAttribute = 'name_ar';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات المنتج')
                    ->schema([
                        Forms\Components\TextInput::make('name_ar')
                            ->label('الاسم بالعربية')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('name_en')
                            ->label('الاسم بالإنجليزية')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Select::make('category_id')
                            ->label('الفئة')
                            ->relationship('category', 'name_ar')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\TextInput::make('price')
                            ->label('السعر')
                            ->numeric()
                            ->prefix('ج.م')
                            ->required(),

                        Forms\Components\TextInput::make('weight')
                            ->label('الوزن')
                            ->numeric()
                            ->suffix('جرام')
                            ->required(),

                        Forms\Components\Select::make('material_type')
                            ->label('نوع المادة')
                            ->options([
                                'gold' => 'ذهب',
                                'silver' => 'فضة',
                                'platinum' => 'بلاتين',
                                'diamond' => 'ألماس',
                            ])
                            ->required(),

                        Forms\Components\Select::make('metal_purity')
                            ->label('نقاء المعدن')
                            ->options([
                                '24K' => '24 قيراط',
                                '22K' => '22 قيراط',
                                '21K' => '21 قيراط',
                                '18K' => '18 قيراط',
                                '14K' => '14 قيراط',
                                '925' => 'فضة 925',
                                '950' => 'بلاتين 950',
                            ])
                            ->required(),

                        Forms\Components\Toggle::make('is_active')
                            ->label('نشط')
                            ->default(true),

                        Forms\Components\Toggle::make('is_featured')
                            ->label('مميز')
                            ->default(false),
                    ])->columns(2),

                Forms\Components\Section::make('الوصف')
                    ->schema([
                        Forms\Components\Textarea::make('description_ar')
                            ->label('الوصف بالعربية')
                            ->maxLength(65535)
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('description_en')
                            ->label('الوصف بالإنجليزية')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('الصور')
                    ->schema([
                        Forms\Components\FileUpload::make('images')
                            ->label('الصور')
                            ->image()
                            ->multiple()
                            ->directory('products')
                            ->maxSize(2048)
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')
                    ->label('الاسم بالعربية')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('name_en')
                    ->label('الاسم بالإنجليزية')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('category.name_ar')
                    ->label('الفئة')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('price')
                    ->label('السعر')
                    ->money('EGP')
                    ->sortable(),

                Tables\Columns\TextColumn::make('weight')
                    ->label('الوزن')
                    ->suffix(' جرام')
                    ->sortable(),

                Tables\Columns\TextColumn::make('material_type')
                    ->label('نوع المادة')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'gold' => 'ذهب',
                        'silver' => 'فضة',
                        'platinum' => 'بلاتين',
                        'diamond' => 'ألماس',
                        default => $state,
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('metal_purity')
                    ->label('نقاء المعدن')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean(),

                Tables\Columns\IconColumn::make('is_featured')
                    ->label('مميز')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category_id')
                    ->label('الفئة')
                    ->relationship('category', 'name_ar')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('material_type')
                    ->label('نوع المادة')
                    ->options([
                        'gold' => 'ذهب',
                        'silver' => 'فضة',
                        'platinum' => 'بلاتين',
                        'diamond' => 'ألماس',
                    ]),

                Tables\Filters\SelectFilter::make('metal_purity')
                    ->label('نقاء المعدن')
                    ->options([
                        '24K' => '24 قيراط',
                        '22K' => '22 قيراط',
                        '21K' => '21 قيراط',
                        '18K' => '18 قيراط',
                        '14K' => '14 قيراط',
                        '925' => 'فضة 925',
                        '950' => 'بلاتين 950',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('نشط'),

                Tables\Filters\TernaryFilter::make('is_featured')
                    ->label('مميز'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('تفعيل')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true]))
                        ->icon('heroicon-o-check'),
                    Tables\Actions\BulkAction::make('تعطيل')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => false]))
                        ->icon('heroicon-o-x-mark'),
                    Tables\Actions\BulkAction::make('تمييز')
                        ->action(fn (Collection $records) => $records->each->update(['is_featured' => true]))
                        ->icon('heroicon-o-star'),
                    Tables\Actions\BulkAction::make('إلغاء التمييز')
                        ->action(fn (Collection $records) => $records->each->update(['is_featured' => false]))
                        ->icon('heroicon-o-x-circle'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }
}
