<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LanguageResource\Pages;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;


class LanguageResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = \App\Models\Language::class;

    protected static ?string $navigationIcon = 'heroicon-o-language';

    protected static ?string $navigationGroup = 'إعدادات النظام المتقدمة';

    protected static ?int $navigationSort = 4;

    public static function shouldRegisterNavigation(): bool
    {
        return true; // إظهار المورد في القائمة الجانبية
    }

    public static function getNavigationLabel(): string
    {
        return __('Languages');
    }

    public static function getPluralLabel(): string
    {
        return __('Languages');
    }

    public static function getLabel(): string
    {
        return __('Language');
    }

    public static function getNavigationBadge(): ?string
    {
        return count(self::getAvailableLanguages());
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('Language Settings'))
                    ->schema([
                        Forms\Components\TextInput::make('key')
                            ->label(__('Key'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('ar')
                            ->label(__('Arabic'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('en')
                            ->label(__('English'))
                            ->required()
                            ->maxLength(255),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->label(__('Key'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('ar')
                    ->label(__('Arabic'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('en')
                    ->label(__('English'))
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('edit')
                    ->label(__('Edit'))
                    ->icon('heroicon-o-pencil')
                    ->modalHeading(fn ($record) => __('Edit Translation') . ': ' . $record->key)
                    ->form([
                        Forms\Components\TextInput::make('key')
                            ->label(__('Key'))
                            ->disabled()
                            ->default(fn ($record) => $record->key),
                        Forms\Components\TextInput::make('ar')
                            ->label(__('Arabic'))
                            ->required()
                            ->default(fn ($record) => $record->ar ?? ''),
                        Forms\Components\TextInput::make('en')
                            ->label(__('English'))
                            ->required()
                            ->default(fn ($record) => $record->en ?? ''),
                    ])
                    ->action(function (array $data, $record) {
                        self::updateTranslation($record->key, $data);
                    }),
                Tables\Actions\Action::make('delete')
                    ->label(__('Delete'))
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->modalHeading(fn ($record) => __('Delete Translation') . ': ' . $record->key)
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        self::deleteTranslation($record->key);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    //
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\Action::make('create')
                    ->label(__('Add Translation'))
                    ->modalHeading(__('Add Translation'))
                    ->form([
                        Forms\Components\TextInput::make('key')
                            ->label(__('Key'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('ar')
                            ->label(__('Arabic'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('en')
                            ->label(__('English'))
                            ->required()
                            ->maxLength(255),
                    ])
                    ->action(function (array $data) {
                        self::addTranslation($data);
                    }),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageLanguages::route('/'),
        ];
    }

    public static function getAvailableLanguages(): array
    {
        return \App\Models\Language::getAvailableLanguages();
    }

    public static function getTranslations(): array
    {
        return \App\Models\Language::getTranslations();
    }

    public static function updateTranslation(string $key, array $data): void
    {
        \App\Models\Language::updateTranslation($key, $data);
    }

    public static function addTranslation(array $data): void
    {
        \App\Models\Language::addTranslation($data);
    }

    public static function deleteTranslation(string $key): void
    {
        \App\Models\Language::deleteTranslation($key);
    }
}
