<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LanguageManagerResource\Pages;
use App\Models\Language;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\HasPermissionFiltering;

class LanguageManagerResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = Language::class;

    protected static ?string $navigationIcon = 'heroicon-o-globe-alt';

    protected static ?string $navigationGroup = 'إعدادات النظام المتقدمة';

    protected static ?int $navigationSort = 3;

    public static function getNavigationLabel(): string
    {
        return __('إدارة اللغات');
    }

    public static function getPluralLabel(): string
    {
        return __('اللغات المتاحة');
    }

    public static function getLabel(): string
    {
        return __('اللغة');
    }

    public static function getNavigationBadge(): ?string
    {
        return Language::count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('Language Information'))
                    ->schema([
                        Forms\Components\TextInput::make('code')
                            ->label(__('Language Code'))
                            ->required()
                            ->maxLength(10),
                        Forms\Components\TextInput::make('name')
                            ->label(__('Language Name'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('native_name')
                            ->label(__('Native Name'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Toggle::make('is_rtl')
                            ->label(__('Is RTL'))
                            ->helperText(__('Right-to-left language')),
                        Forms\Components\Toggle::make('is_default')
                            ->label(__('Is Default'))
                            ->helperText(__('Default language for the site')),
                        Forms\Components\Toggle::make('is_active')
                            ->label(__('Is Active'))
                            ->default(true),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->label(__('Code'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('Name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('native_name')
                    ->label(__('Native Name'))
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_rtl')
                    ->label(__('RTL'))
                    ->boolean(),
                Tables\Columns\IconColumn::make('is_default')
                    ->label(__('Default'))
                    ->boolean(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label(__('Active'))
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Created At'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('Updated At'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLanguages::route('/'),
            'create' => Pages\CreateLanguage::route('/create'),
            'edit' => Pages\EditLanguage::route('/{record}/edit'),
        ];
    }
}
