<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RoleResource\Pages;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Grid;
use Filament\Tables\Columns\TextColumn;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    protected static ?string $navigationLabel = 'الأدوار';
    protected static ?string $navigationGroup = 'إدارة الصلاحيات';
    protected static ?int $navigationSort = 1;

    public static function getModelLabel(): string
    {
        return __('دور');
    }

    public static function getPluralModelLabel(): string
    {
        return __('الأدوار');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('معلومات الدور الأساسية')
                    ->description('قم بتعيين اسم الدور ونوع الحارس المناسب')
                    ->icon('heroicon-o-identification')
                    ->schema([
                        TextInput::make('name')
                            ->label('اسم الدور (بالإنجليزية)')
                            ->helperText('اسم الدور باللغة الإنجليزية (مثل: admin, manager, user)')
                            ->placeholder('مثال: admin')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->regex('/^[a-z_]+$/')
                            ->validationMessages([
                                'regex' => 'يجب أن يحتوي اسم الدور على أحرف إنجليزية صغيرة وشرطة سفلية فقط',
                            ]),

                        Select::make('guard_name')
                            ->label('نوع الحارس')
                            ->helperText('اختر نوع الحارس المناسب للدور')
                            ->options([
                                'web' => 'واجهة الويب (Web)',
                                'api' => 'واجهة برمجة التطبيقات (API)',
                            ])
                            ->default('web')
                            ->required(),
                    ])->columns(1),

                Section::make('الصلاحيات والأذونات')
                    ->description('اختر الصلاحيات التي تريد منحها لهذا الدور. الصلاحيات مجمعة حسب المورد لسهولة الإدارة')
                    ->icon('heroicon-o-shield-check')
                    ->schema([
                        static::getGeneralControlsSchema(),
                        ...static::getResourcePermissionSections(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('اسم الدور')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->formatStateUsing(fn (string $state): string => static::getRoleDisplayName($state))
                    ->description(fn (Role $record): string => static::getRoleDescription($record->name)),

                TextColumn::make('guard_name')
                    ->label('نوع الحارس')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'web' => 'واجهة الويب',
                        'api' => 'واجهة برمجة التطبيقات',
                        default => $state,
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'web' => 'success',
                        'api' => 'warning',
                        default => 'gray',
                    }),

                TextColumn::make('permissions_count')
                    ->label('عدد الصلاحيات')
                    ->counts('permissions')
                    ->badge()
                    ->color('info')
                    ->sortable(),

                TextColumn::make('users_count')
                    ->label('عدد المستخدمين')
                    ->counts('users')
                    ->badge()
                    ->color('primary')
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('آخر تحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('guard_name')
                    ->label('نوع الحارس')
                    ->options([
                        'web' => 'واجهة الويب',
                        'api' => 'واجهة برمجة التطبيقات',
                    ])
                    ->placeholder('جميع الأنواع'),

                Tables\Filters\Filter::make('has_users')
                    ->label('الأدوار المستخدمة')
                    ->query(fn ($query) => $query->has('users'))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض')
                    ->color('info'),
                Tables\Actions\EditAction::make()
                    ->label('تعديل')
                    ->color('warning'),
                Tables\Actions\DeleteAction::make()
                    ->label('حذف')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('حذف الدور')
                    ->modalDescription('هل أنت متأكد من حذف هذا الدور؟ سيتم إزالة جميع الصلاحيات المرتبطة به.')
                    ->modalSubmitActionLabel('نعم، احذف')
                    ->modalCancelActionLabel('إلغاء')
                    ->before(function (Role $record) {
                        // منع حذف دور super_admin
                        if ($record->name === 'super_admin') {
                            throw new \Exception('لا يمكن حذف دور السوبر أدمن لأسباب أمنية');
                        }

                        // منع حذف الأدوار التي لها مستخدمين
                        if ($record->users()->count() > 0) {
                            throw new \Exception('لا يمكن حذف هذا الدور لأنه مُعيَّن لـ ' . $record->users()->count() . ' مستخدم');
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد')
                        ->requiresConfirmation()
                        ->modalHeading('حذف الأدوار المحددة')
                        ->modalDescription('هل أنت متأكد من حذف الأدوار المحددة؟')
                        ->modalSubmitActionLabel('نعم، احذف')
                        ->modalCancelActionLabel('إلغاء')
                        ->before(function ($records) {
                            // منع حذف دور super_admin
                            foreach ($records as $record) {
                                if ($record->name === 'super_admin') {
                                    throw new \Exception('لا يمكن حذف دور السوبر أدمن لأسباب أمنية');
                                }

                                // منع حذف الأدوار التي لها مستخدمين
                                if ($record->users()->count() > 0) {
                                    throw new \Exception('لا يمكن حذف الدور "' . static::getRoleDisplayName($record->name) . '" لأنه مُعيَّن لـ ' . $record->users()->count() . ' مستخدم');
                                }
                            }
                        }),
                ]),
            ])
            ->emptyStateHeading('لا توجد أدوار')
            ->emptyStateDescription('لم يتم إنشاء أي أدوار بعد. ابدأ بإنشاء دور جديد.')
            ->emptyStateIcon('heroicon-o-shield-exclamation');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }



    /**
     * إنشاء قسم التحكم العام
     */
    protected static function getGeneralControlsSchema()
    {
        return Section::make('أدوات التحكم السريع')
            ->description('استخدم هذه الأدوات لتحديد الصلاحيات بسرعة')
            ->icon('heroicon-o-bolt')
            ->collapsible()
            ->collapsed(false)
            ->schema([
                Grid::make(2)
                    ->schema([
                        Actions::make([
                            Action::make('select_all_permissions')
                                ->label('تحديد جميع الصلاحيات')
                                ->icon('heroicon-o-check-circle')
                                ->color('success')
                                ->size('lg')
                                ->action(function ($livewire) {
                                    $allPermissionIds = Permission::pluck('id')->toArray();
                                    $livewire->data['permissions'] = $allPermissionIds;
                                }),

                            Action::make('deselect_all_permissions')
                                ->label('إلغاء تحديد جميع الصلاحيات')
                                ->icon('heroicon-o-x-circle')
                                ->color('danger')
                                ->size('lg')
                                ->action(function ($livewire) {
                                    $livewire->data['permissions'] = [];
                                }),
                        ])->fullWidth(),
                    ]),
            ]);
    }

    /**
     * إنشاء أقسام الصلاحيات مجمعة حسب المورد
     */
    protected static function getResourcePermissionSections(): array
    {
        $groupedPermissions = static::getGroupedPermissions();
        $sections = [];

        foreach ($groupedPermissions as $resourceKey => $resourceData) {
            $sections[] = static::createResourcePermissionSection($resourceKey, $resourceData);
        }

        return $sections;
    }

    /**
     * إنشاء قسم صلاحيات مورد معين
     */
    protected static function createResourcePermissionSection(string $resourceKey, array $resourceData)
    {
        return Section::make($resourceData['label'])
            ->description($resourceData['description'])
            ->icon($resourceData['icon'])
            ->collapsible()
            ->collapsed(false)
            ->headerActions([
                Action::make("select_all_{$resourceKey}")
                    ->label('تحديد الكل')
                    ->icon('heroicon-o-check-circle')
                    ->size('sm')
                    ->color('success')
                    ->action(function ($livewire) use ($resourceData) {
                        $currentPermissions = $livewire->data['permissions'] ?? [];
                        $resourcePermissionIds = array_keys($resourceData['permissions']);
                        $newPermissions = array_unique(array_merge($currentPermissions, $resourcePermissionIds));
                        $livewire->data['permissions'] = $newPermissions;
                    }),

                Action::make("deselect_all_{$resourceKey}")
                    ->label('إلغاء تحديد الكل')
                    ->icon('heroicon-o-x-circle')
                    ->size('sm')
                    ->color('danger')
                    ->action(function ($livewire) use ($resourceData) {
                        $currentPermissions = $livewire->data['permissions'] ?? [];
                        $resourcePermissionIds = array_keys($resourceData['permissions']);
                        $newPermissions = array_diff($currentPermissions, $resourcePermissionIds);
                        $livewire->data['permissions'] = array_values($newPermissions);
                    }),
            ])
            ->schema([
                CheckboxList::make('permissions')
                    ->hiddenLabel()
                    ->relationship('permissions', 'name')
                    ->options($resourceData['permissions'])
                    ->columns(2)
                    ->gridDirection('row')
                    ->searchable()
                    ->bulkToggleable(),
            ]);
    }



    /**
     * ترجمة أسماء الصلاحيات إلى العربية
     */
    protected static function getTranslatedPermissions(): array
    {
        $permissions = Permission::all();
        $translatedPermissions = [];

        foreach ($permissions as $permission) {
            $translatedPermissions[$permission->id] = static::translatePermissionName($permission->name);
        }

        return $translatedPermissions;
    }

    /**
     * ترجمة اسم الصلاحية الواحدة
     */
    protected static function translatePermissionName(string $permissionName): string
    {
        // تقسيم اسم الصلاحية إلى أجزاء
        $parts = explode('_', $permissionName);

        if (count($parts) < 2) {
            return $permissionName;
        }

        $action = $parts[0]; // الإجراء (view, create, update, delete)
        $resource = implode('_', array_slice($parts, 1)); // اسم المورد

        // ترجمة الإجراءات
        $actionTranslations = [
            'view' => 'عرض',
            'view_any' => 'عرض جميع',
            'create' => 'إنشاء',
            'update' => 'تعديل',
            'edit' => 'تعديل',
            'delete' => 'حذف',
            'delete_any' => 'حذف جميع',
            'force_delete' => 'حذف نهائي',
            'force_delete_any' => 'حذف نهائي لجميع',
            'restore' => 'استعادة',
            'restore_any' => 'استعادة جميع',
            'replicate' => 'تكرار',
            'reorder' => 'إعادة ترتيب',
            'manage' => 'إدارة',
            'access' => 'الوصول إلى',
            'export' => 'تصدير',
            'import' => 'استيراد',
            'publish' => 'نشر',
            'unpublish' => 'إلغاء نشر',
            'approve' => 'موافقة',
            'reject' => 'رفض',
            'assign' => 'تعيين',
            'unassign' => 'إلغاء تعيين',
        ];

        // ترجمة الموارد
        $resourceTranslations = [
            'user' => 'المستخدمين',
            'role' => 'الأدوار',
            'permission' => 'الصلاحيات',
            'product' => 'المنتجات',
            'category' => 'الفئات',
            'order' => 'الطلبات',
            'appointment' => 'المواعيد',
            'blog::post' => 'مقالات المدونة',
            'page' => 'الصفحات',
            'feature' => 'الميزات',
            'home::slider' => 'شرائح الصفحة الرئيسية',
            'job' => 'الوظائف',
            'job::application' => 'طلبات الوظائف',
            'language' => 'اللغات',
            'language::manager' => 'مدير اللغات',
            'metal::price' => 'أسعار المعادن',
            'metal::purity' => 'عيارات المعادن',
            'metal::type' => 'أنواع المعادن',
            'newsletter' => 'النشرة البريدية',
            'setting::change' => 'تغيير الإعدادات',
            'site::setting' => 'إعدادات الموقع',
            'store' => 'المتاجر',
            'super::admin::setting' => 'إعدادات السوبر أدمن',
            'testimonial' => 'الشهادات',
            'review' => 'التقييمات',
            'comment' => 'التعليقات',
            'media' => 'الوسائط',
            'file' => 'الملفات',
            'image' => 'الصور',
            'video' => 'الفيديوهات',
            'notification' => 'الإشعارات',
            'message' => 'الرسائل',
            'email' => 'البريد الإلكتروني',
            'sms' => 'الرسائل النصية',
            'report' => 'التقارير',
            'analytics' => 'التحليلات',
            'backup' => 'النسخ الاحتياطية',
            'log' => 'السجلات',
            'cache' => 'التخزين المؤقت',
            'queue' => 'قائمة الانتظار',
            'widget_StatsOverview' => 'ودجة الإحصائيات',
            'widget_SalesChart' => 'ودجة مخطط المبيعات',
            'widget_GoldPriceChart' => 'ودجة مخطط أسعار الذهب',
            'widget_LatestOrders' => 'ودجة أحدث الطلبات',
            'widget_UpcomingAppointments' => 'ودجة المواعيد القادمة',
            'widget_MaintenanceModeToggle' => 'ودجة وضع الصيانة',
            'page_DashboardPage' => 'صفحة لوحة التحكم',
            'page_PreviewSettings' => 'صفحة معاينة الإعدادات',
            'page_SearchSettings' => 'صفحة إعدادات البحث',
            'page_SiteSettingsManager' => 'صفحة مدير إعدادات الموقع',
            'page_TranslationsManager' => 'صفحة مدير الترجمات',
        ];

        $translatedAction = $actionTranslations[$action] ?? $action;
        $translatedResource = $resourceTranslations[$resource] ?? $resource;

        return "{$translatedAction} {$translatedResource}";
    }

    /**
     * الحصول على الاسم المعروض للدور
     */
    protected static function getRoleDisplayName(string $roleName): string
    {
        $roleNames = [
            'super_admin' => 'السوبر أدمن',
            'admin' => 'المدير',
            'manager' => 'المدير المساعد',
            'user' => 'المستخدم',
            'customer' => 'العميل',
            'employee' => 'الموظف',
            'moderator' => 'المشرف',
        ];

        return $roleNames[$roleName] ?? $roleName;
    }

    /**
     * الحصول على وصف الدور
     */
    protected static function getRoleDescription(string $roleName): string
    {
        $descriptions = [
            'super_admin' => 'صلاحيات كاملة لإدارة النظام والمستخدمين',
            'admin' => 'صلاحيات إدارية شاملة عدا إعدادات النظام الحساسة',
            'manager' => 'صلاحيات محدودة لإدارة المحتوى والطلبات',
            'user' => 'مستخدم عادي بدون صلاحيات إدارية',
            'customer' => 'عميل يمكنه التسوق وإدارة حسابه',
            'employee' => 'موظف بصلاحيات محددة حسب القسم',
            'moderator' => 'مشرف على المحتوى والتعليقات',
        ];

        return $descriptions[$roleName] ?? 'دور مخصص';
    }

    /**
     * تجميع الصلاحيات حسب المورد
     */
    protected static function getGroupedPermissions(): array
    {
        $permissions = Permission::all();
        $groupedPermissions = [];

        // تعريف المجموعات والموارد
        $resourceGroups = [
            'users_roles' => [
                'label' => 'المستخدمين والأدوار',
                'icon' => 'heroicon-o-users',
                'description' => 'صلاحيات إدارة المستخدمين والأدوار والصلاحيات',
                'resources' => ['user', 'role', 'permission'],
            ],
            'products_categories' => [
                'label' => 'المنتجات والفئات',
                'icon' => 'heroicon-o-cube',
                'description' => 'صلاحيات إدارة المنتجات والفئات والمخزون',
                'resources' => ['product', 'category'],
            ],
            'orders_sales' => [
                'label' => 'الطلبات والمبيعات',
                'icon' => 'heroicon-o-shopping-cart',
                'description' => 'صلاحيات إدارة الطلبات والمبيعات والفواتير',
                'resources' => ['order'],
            ],
            'appointments_bookings' => [
                'label' => 'المواعيد والحجوزات',
                'icon' => 'heroicon-o-calendar',
                'description' => 'صلاحيات إدارة المواعيد والحجوزات',
                'resources' => ['appointment'],
            ],
            'content_management' => [
                'label' => 'إدارة المحتوى',
                'icon' => 'heroicon-o-document-text',
                'description' => 'صلاحيات إدارة المحتوى والصفحات والمقالات',
                'resources' => ['page', 'blog::post', 'feature', 'home::slider', 'testimonial'],
            ],
            'metals_prices' => [
                'label' => 'المعادن والأسعار',
                'icon' => 'heroicon-o-currency-dollar',
                'description' => 'صلاحيات إدارة أسعار المعادن وأنواعها وعياراتها',
                'resources' => ['metal::price', 'metal::type', 'metal::purity'],
            ],
            'system_settings' => [
                'label' => 'إعدادات النظام',
                'icon' => 'heroicon-o-cog-6-tooth',
                'description' => 'صلاحيات إدارة إعدادات النظام والموقع',
                'resources' => ['site::setting', 'super::admin::setting', 'setting::change'],
            ],
            'jobs_applications' => [
                'label' => 'الوظائف والطلبات',
                'icon' => 'heroicon-o-briefcase',
                'description' => 'صلاحيات إدارة الوظائف وطلبات التوظيف',
                'resources' => ['job', 'job::application'],
            ],
            'communication' => [
                'label' => 'التواصل والإشعارات',
                'icon' => 'heroicon-o-chat-bubble-left-right',
                'description' => 'صلاحيات إدارة النشرة البريدية والإشعارات والرسائل',
                'resources' => ['newsletter', 'notification', 'message', 'email', 'sms'],
            ],
            'system_management' => [
                'label' => 'إدارة النظام المتقدمة',
                'icon' => 'heroicon-o-server',
                'description' => 'صلاحيات إدارة اللغات والمتاجر والنظام',
                'resources' => ['language', 'language::manager', 'store'],
            ],
            'media_files' => [
                'label' => 'الوسائط والملفات',
                'icon' => 'heroicon-o-photo',
                'description' => 'صلاحيات إدارة الوسائط والملفات والصور',
                'resources' => ['media', 'file', 'image', 'video'],
            ],
            'reports_analytics' => [
                'label' => 'التقارير والتحليلات',
                'icon' => 'heroicon-o-chart-pie',
                'description' => 'صلاحيات عرض التقارير والتحليلات والإحصائيات',
                'resources' => ['report', 'analytics'],
            ],
            'system_tools' => [
                'label' => 'أدوات النظام',
                'icon' => 'heroicon-o-wrench-screwdriver',
                'description' => 'صلاحيات إدارة النسخ الاحتياطية والسجلات والتخزين المؤقت',
                'resources' => ['backup', 'log', 'cache', 'queue'],
            ],
            'dashboard_widgets' => [
                'label' => 'لوحة التحكم والودجات',
                'icon' => 'heroicon-o-chart-bar',
                'description' => 'صلاحيات الوصول للوحة التحكم والودجات والصفحات الخاصة',
                'resources' => [],
            ],
        ];

        // تجميع الصلاحيات
        foreach ($resourceGroups as $groupKey => $groupData) {
            $groupedPermissions[$groupKey] = [
                'label' => $groupData['label'],
                'icon' => $groupData['icon'],
                'description' => $groupData['description'],
                'permissions' => [],
            ];

            foreach ($permissions as $permission) {
                $permissionName = $permission->name;

                // التحقق من الودجات والصفحات
                if ($groupKey === 'dashboard_widgets') {
                    if (str_starts_with($permissionName, 'widget_') || str_starts_with($permissionName, 'page_')) {
                        $groupedPermissions[$groupKey]['permissions'][$permission->id] = static::translatePermissionName($permissionName);
                    }
                    continue;
                }

                // التحقق من الموارد العادية
                foreach ($groupData['resources'] as $resource) {
                    if (str_contains($permissionName, $resource)) {
                        $groupedPermissions[$groupKey]['permissions'][$permission->id] = static::translatePermissionName($permissionName);
                        break;
                    }
                }
            }
        }

        // إزالة المجموعات الفارغة
        $groupedPermissions = array_filter($groupedPermissions, function ($group) {
            return !empty($group['permissions']);
        });

        return $groupedPermissions;
    }
}
