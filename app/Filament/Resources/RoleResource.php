<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RoleResource\Pages;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Grid;
use Filament\Tables\Columns\TextColumn;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    protected static ?string $navigationLabel = 'الأدوار';
    protected static ?string $navigationGroup = 'إدارة الصلاحيات';
    protected static ?int $navigationSort = 1;

    public static function getModelLabel(): string
    {
        return 'دور';
    }

    public static function getPluralModelLabel(): string
    {
        return 'الأدوار';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('معلومات الدور الأساسية')
                    ->description('قم بتعيين اسم الدور ونوع الحارس المناسب')
                    ->icon('heroicon-o-identification')
                    ->schema([
                        TextInput::make('name')
                            ->label('اسم الدور (بالإنجليزية)')
                            ->helperText('اسم الدور باللغة الإنجليزية (مثل: admin, manager, user)')
                            ->placeholder('مثال: admin')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->regex('/^[a-z_]+$/')
                            ->validationMessages([
                                'required' => 'اسم الدور مطلوب',
                                'unique' => 'اسم الدور موجود مسبقاً، يرجى اختيار اسم آخر',
                                'max' => 'اسم الدور يجب ألا يتجاوز 255 حرف',
                                'regex' => 'يجب أن يحتوي اسم الدور على أحرف إنجليزية صغيرة وشرطة سفلية فقط',
                            ]),

                        Select::make('guard_name')
                            ->label('نوع الحارس')
                            ->helperText('اختر نوع الحارس المناسب للدور')
                            ->options([
                                'web' => 'واجهة الويب (Web)',
                                'api' => 'واجهة برمجة التطبيقات (API)',
                            ])
                            ->default('web')
                            ->required()
                            ->validationMessages([
                                'required' => 'نوع الحارس مطلوب',
                            ]),
                    ])->columns(1),

                Section::make('الصلاحيات والأذونات')
                    ->description('اختر الصلاحيات التي تريد منحها لهذا الدور. الصلاحيات مجمعة حسب المورد لسهولة الإدارة. يمكنك استخدام أزرار التحكم السريع لتحديد أو إلغاء تحديد الصلاحيات بسرعة.')
                    ->icon('heroicon-o-shield-check')
                    ->schema([
                        static::getGeneralControlsSchema(),
                        ...static::getResourcePermissionSections(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('اسم الدور')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->formatStateUsing(fn (string $state): string => static::getRoleDisplayName($state))
                    ->description(fn (Role $record): string => static::getRoleDescription($record->name)),

                TextColumn::make('guard_name')
                    ->label('نوع الحارس')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'web' => 'واجهة الويب',
                        'api' => 'واجهة برمجة التطبيقات',
                        default => $state,
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'web' => 'success',
                        'api' => 'warning',
                        default => 'gray',
                    }),

                TextColumn::make('permissions_count')
                    ->label('عدد الصلاحيات')
                    ->counts('permissions')
                    ->badge()
                    ->color('info')
                    ->sortable(),

                TextColumn::make('users_count')
                    ->label('عدد المستخدمين')
                    ->counts('users')
                    ->badge()
                    ->color('primary')
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('آخر تحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('guard_name')
                    ->label('نوع الحارس')
                    ->options([
                        'web' => 'واجهة الويب',
                        'api' => 'واجهة برمجة التطبيقات',
                    ])
                    ->placeholder('جميع الأنواع'),

                Tables\Filters\Filter::make('has_users')
                    ->label('الأدوار المستخدمة')
                    ->query(fn ($query) => $query->has('users'))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض')
                    ->color('info'),
                Tables\Actions\EditAction::make()
                    ->label('تعديل')
                    ->color('warning'),
                Tables\Actions\DeleteAction::make()
                    ->label('حذف')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('حذف الدور')
                    ->modalDescription('هل أنت متأكد من حذف هذا الدور؟ سيتم إزالة جميع الصلاحيات المرتبطة به.')
                    ->modalSubmitActionLabel('نعم، احذف')
                    ->modalCancelActionLabel('إلغاء')
                    ->before(function (Role $record) {
                        // منع حذف دور super_admin
                        if ($record->name === 'super_admin') {
                            throw new \Exception('لا يمكن حذف دور السوبر أدمن لأسباب أمنية');
                        }

                        // منع حذف الأدوار التي لها مستخدمين
                        if ($record->users()->count() > 0) {
                            throw new \Exception('لا يمكن حذف هذا الدور لأنه مُعيَّن لـ ' . $record->users()->count() . ' مستخدم');
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد')
                        ->requiresConfirmation()
                        ->modalHeading('حذف الأدوار المحددة')
                        ->modalDescription('هل أنت متأكد من حذف الأدوار المحددة؟')
                        ->modalSubmitActionLabel('نعم، احذف')
                        ->modalCancelActionLabel('إلغاء')
                        ->before(function ($records) {
                            // منع حذف دور super_admin
                            foreach ($records as $record) {
                                if ($record->name === 'super_admin') {
                                    throw new \Exception('لا يمكن حذف دور السوبر أدمن لأسباب أمنية');
                                }

                                // منع حذف الأدوار التي لها مستخدمين
                                if ($record->users()->count() > 0) {
                                    throw new \Exception('لا يمكن حذف الدور "' . static::getRoleDisplayName($record->name) . '" لأنه مُعيَّن لـ ' . $record->users()->count() . ' مستخدم');
                                }
                            }
                        }),
                ]),
            ])
            ->emptyStateHeading('لا توجد أدوار')
            ->emptyStateDescription('لم يتم إنشاء أي أدوار بعد. ابدأ بإنشاء دور جديد.')
            ->emptyStateIcon('heroicon-o-shield-exclamation');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }



    /**
     * إنشاء قسم التحكم العام
     */
    protected static function getGeneralControlsSchema()
    {
        return Section::make('أدوات التحكم السريع')
            ->description('استخدم هذه الأدوات لتحديد أو إلغاء تحديد جميع الصلاحيات في النظام دفعة واحدة')
            ->icon('heroicon-o-bolt')
            ->collapsible()
            ->collapsed(false)
            ->schema([
                Grid::make(2)
                    ->schema([
                        Actions::make([
                            Action::make('select_all_permissions')
                                ->label('تحديد جميع الصلاحيات')
                                ->icon('heroicon-o-check-circle')
                                ->color('success')
                                ->size('lg')
                                ->action(function ($livewire) {
                                    $allPermissionIds = Permission::pluck('id')->toArray();
                                    $livewire->data['permissions'] = $allPermissionIds;
                                }),

                            Action::make('deselect_all_permissions')
                                ->label('إلغاء تحديد جميع الصلاحيات')
                                ->icon('heroicon-o-x-circle')
                                ->color('danger')
                                ->size('lg')
                                ->action(function ($livewire) {
                                    $livewire->data['permissions'] = [];
                                }),
                        ])->fullWidth(),
                    ]),
            ]);
    }

    /**
     * إنشاء أقسام الصلاحيات مجمعة حسب المورد
     */
    protected static function getResourcePermissionSections(): array
    {
        $groupedPermissions = static::getGroupedPermissions();
        $sections = [];

        foreach ($groupedPermissions as $resourceKey => $resourceData) {
            $sections[] = static::createResourcePermissionSection($resourceKey, $resourceData);
        }

        return $sections;
    }

    /**
     * إنشاء قسم صلاحيات مورد معين
     */
    protected static function createResourcePermissionSection(string $resourceKey, array $resourceData)
    {
        return Section::make($resourceData['label'])
            ->description($resourceData['description'])
            ->icon($resourceData['icon'])
            ->collapsible()
            ->collapsed(false)
            ->headerActions([
                Action::make("select_all_{$resourceKey}")
                    ->label('تحديد الكل')
                    ->icon('heroicon-o-check-circle')
                    ->size('sm')
                    ->color('success')
                    ->action(function ($livewire) use ($resourceData) {
                        $currentPermissions = $livewire->data['permissions'] ?? [];
                        $resourcePermissionIds = array_keys($resourceData['permissions']);
                        $newPermissions = array_unique(array_merge($currentPermissions, $resourcePermissionIds));
                        $livewire->data['permissions'] = $newPermissions;
                    }),

                Action::make("deselect_all_{$resourceKey}")
                    ->label('إلغاء تحديد الكل')
                    ->icon('heroicon-o-x-circle')
                    ->size('sm')
                    ->color('danger')
                    ->action(function ($livewire) use ($resourceData) {
                        $currentPermissions = $livewire->data['permissions'] ?? [];
                        $resourcePermissionIds = array_keys($resourceData['permissions']);
                        $newPermissions = array_diff($currentPermissions, $resourcePermissionIds);
                        $livewire->data['permissions'] = array_values($newPermissions);
                    }),
            ])
            ->schema([
                CheckboxList::make('permissions')
                    ->hiddenLabel()
                    ->relationship('permissions', 'name')
                    ->options($resourceData['permissions'])
                    ->columns(2)
                    ->gridDirection('row')
                    ->searchable()
                    ->bulkToggleable()
                    ->helperText('اختر الصلاحيات المناسبة لهذا المورد'),
            ]);
    }



    /**
     * ترجمة أسماء الصلاحيات إلى العربية
     */
    protected static function getTranslatedPermissions(): array
    {
        $permissions = Permission::all();
        $translatedPermissions = [];

        foreach ($permissions as $permission) {
            $translatedPermissions[$permission->id] = static::translatePermissionName($permission->name);
        }

        return $translatedPermissions;
    }

    /**
     * ترجمة اسم الصلاحية الواحدة
     */
    protected static function translatePermissionName(string $permissionName): string
    {
        // التعامل مع صلاحيات الصفحات والودجات
        if (str_starts_with($permissionName, 'page_')) {
            $pageName = str_replace('page_', '', $permissionName);
            return "صفحة {$pageName}";
        }

        if (str_starts_with($permissionName, 'widget_')) {
            $widgetName = str_replace('widget_', '', $permissionName);
            return "ودجة {$widgetName}";
        }

        // التعامل مع view_any_ خاص
        if (str_starts_with($permissionName, 'view_any_')) {
            $resource = str_replace('view_any_', '', $permissionName);
            $action = 'view_any';
        } else {
            // تقسيم اسم الصلاحية إلى أجزاء
            $parts = explode('_', $permissionName);

            if (count($parts) < 2) {
                return $permissionName;
            }

            $action = $parts[0]; // الإجراء (create, update, delete)
            $resource = implode('_', array_slice($parts, 1)); // اسم المورد
        }

        // ترجمة الإجراءات المبسطة
        $actionTranslations = [
            'view_any' => 'عرض جميع',
            'create' => 'إنشاء',
            'update' => 'تعديل',
            'delete' => 'حذف',
            'page' => 'صفحة',
            'widget' => 'ودجة',
        ];

        // ترجمة الموارد الأساسية
        $resourceTranslations = [
            'user' => 'المستخدمين',
            'role' => 'الأدوار',
            'permission' => 'الصلاحيات',
            'product' => 'المنتجات',
            'category' => 'الفئات',
            'order' => 'الطلبات',
            'appointment' => 'المواعيد',
            'blog::post' => 'مقالات المدونة',
            'page' => 'الصفحات',
            'feature' => 'الميزات',
            'testimonial' => 'الشهادات',
            'job' => 'الوظائف',
            'job::application' => 'طلبات الوظائف',
            'language' => 'اللغات',
            'language::manager' => 'مدير اللغات',
            'metal::price' => 'أسعار المعادن',
            'metal::purity' => 'عيارات المعادن',
            'metal::type' => 'أنواع المعادن',
            'newsletter' => 'النشرة البريدية',
            'site::setting' => 'إعدادات الموقع',
            'setting::change' => 'تغيير الإعدادات',
            'store' => 'المتاجر',
            'super::admin::setting' => 'إعدادات السوبر أدمن',
            'home::slider' => 'شرائح الصفحة الرئيسية',
            'media' => 'الوسائط',
            'address' => 'العناوين',
            'notification' => 'الإشعارات',
            'review' => 'التقييمات',
            'wishlist' => 'قوائم الرغبات',
            'cart' => 'سلات التسوق',
            'setting' => 'الإعدادات',
        ];

        $translatedAction = $actionTranslations[$action] ?? $action;
        $translatedResource = $resourceTranslations[$resource] ?? $resource;

        return "{$translatedAction} {$translatedResource}";
    }

    /**
     * الحصول على الاسم المعروض للدور
     */
    protected static function getRoleDisplayName(string $roleName): string
    {
        $roleNames = [
            'super_admin' => 'السوبر أدمن',
            'admin' => 'المدير',
            'manager' => 'المدير المساعد',
            'user' => 'المستخدم',
            'customer' => 'العميل',
            'employee' => 'الموظف',
            'moderator' => 'المشرف',
        ];

        return $roleNames[$roleName] ?? $roleName;
    }

    /**
     * الحصول على وصف الدور
     */
    protected static function getRoleDescription(string $roleName): string
    {
        $descriptions = [
            'super_admin' => 'صلاحيات كاملة لإدارة النظام والمستخدمين',
            'admin' => 'صلاحيات إدارية شاملة عدا إعدادات النظام الحساسة',
            'manager' => 'صلاحيات محدودة لإدارة المحتوى والطلبات',
            'user' => 'مستخدم عادي بدون صلاحيات إدارية',
            'customer' => 'عميل يمكنه التسوق وإدارة حسابه',
            'employee' => 'موظف بصلاحيات محددة حسب القسم',
            'moderator' => 'مشرف على المحتوى والتعليقات',
        ];

        return $descriptions[$roleName] ?? 'دور مخصص';
    }

    /**
     * تجميع الصلاحيات حسب المورد - كل مورد في قسم منفصل
     */
    protected static function getGroupedPermissions(): array
    {
        $permissions = Permission::all();
        $groupedPermissions = [];

        // تعريف الموارد المنفصلة مع ترتيب منطقي متدرج
        $resourceGroups = [
            // أ. إدارة النظام الأساسية
            'user' => [
                'label' => 'المستخدمين',
                'icon' => 'heroicon-o-users',
                'description' => 'صلاحيات إدارة المستخدمين وحساباتهم',
                'color' => 'blue',
                'category' => 'إدارة النظام الأساسية',
            ],
            'role' => [
                'label' => 'الأدوار',
                'icon' => 'heroicon-o-shield-check',
                'description' => 'صلاحيات إدارة أدوار المستخدمين',
                'color' => 'indigo',
                'category' => 'إدارة النظام الأساسية',
            ],
            'permission' => [
                'label' => 'الصلاحيات',
                'icon' => 'heroicon-o-key',
                'description' => 'صلاحيات إدارة صلاحيات النظام',
                'color' => 'purple',
                'category' => 'إدارة النظام الأساسية',
            ],

            // ب. إدارة المحتوى والمنتجات
            'product' => [
                'label' => 'المنتجات',
                'icon' => 'heroicon-o-cube',
                'description' => 'صلاحيات إدارة المنتجات والمخزون',
                'color' => 'green',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'category' => [
                'label' => 'الفئات',
                'icon' => 'heroicon-o-tag',
                'description' => 'صلاحيات إدارة فئات المنتجات',
                'color' => 'emerald',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'page' => [
                'label' => 'الصفحات',
                'icon' => 'heroicon-o-document-text',
                'description' => 'صلاحيات إدارة صفحات الموقع',
                'color' => 'teal',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'blog::post' => [
                'label' => 'مقالات المدونة',
                'icon' => 'heroicon-o-newspaper',
                'description' => 'صلاحيات إدارة مقالات المدونة',
                'color' => 'cyan',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'feature' => [
                'label' => 'الميزات',
                'icon' => 'heroicon-o-star',
                'description' => 'صلاحيات إدارة ميزات الموقع',
                'color' => 'sky',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'testimonial' => [
                'label' => 'الشهادات',
                'icon' => 'heroicon-o-chat-bubble-left-ellipsis',
                'description' => 'صلاحيات إدارة شهادات العملاء',
                'color' => 'blue',
                'category' => 'إدارة المحتوى والمنتجات',
            ],

            // ج. إدارة العمليات التجارية
            'order' => [
                'label' => 'الطلبات',
                'icon' => 'heroicon-o-shopping-cart',
                'description' => 'صلاحيات إدارة طلبات العملاء',
                'color' => 'orange',
                'category' => 'إدارة العمليات التجارية',
            ],
            'appointment' => [
                'label' => 'المواعيد',
                'icon' => 'heroicon-o-calendar',
                'description' => 'صلاحيات إدارة مواعيد العملاء',
                'color' => 'amber',
                'category' => 'إدارة العمليات التجارية',
            ],
            'metal::price' => [
                'label' => 'أسعار المعادن',
                'icon' => 'heroicon-o-currency-dollar',
                'description' => 'صلاحيات إدارة أسعار المعادن',
                'color' => 'yellow',
                'category' => 'إدارة العمليات التجارية',
            ],
            'metal::type' => [
                'label' => 'أنواع المعادن',
                'icon' => 'heroicon-o-squares-2x2',
                'description' => 'صلاحيات إدارة أنواع المعادن',
                'color' => 'lime',
                'category' => 'إدارة العمليات التجارية',
            ],
            'metal::purity' => [
                'label' => 'عيارات المعادن',
                'icon' => 'heroicon-o-beaker',
                'description' => 'صلاحيات إدارة عيارات المعادن',
                'color' => 'green',
                'category' => 'إدارة العمليات التجارية',
            ],

            // د. إدارة الموارد البشرية والتواصل
            'job' => [
                'label' => 'الوظائف',
                'icon' => 'heroicon-o-briefcase',
                'description' => 'صلاحيات إدارة الوظائف المتاحة',
                'color' => 'violet',
                'category' => 'إدارة الموارد البشرية والتواصل',
            ],
            'job::application' => [
                'label' => 'طلبات الوظائف',
                'icon' => 'heroicon-o-document-check',
                'description' => 'صلاحيات إدارة طلبات التوظيف',
                'color' => 'purple',
                'category' => 'إدارة الموارد البشرية والتواصل',
            ],
            'newsletter' => [
                'label' => 'النشرة البريدية',
                'icon' => 'heroicon-o-envelope',
                'description' => 'صلاحيات إدارة النشرة البريدية',
                'color' => 'fuchsia',
                'category' => 'إدارة الموارد البشرية والتواصل',
            ],

            // هـ. إعدادات النظام المتقدمة
            'site::setting' => [
                'label' => 'إعدادات الموقع',
                'icon' => 'heroicon-o-cog-6-tooth',
                'description' => 'صلاحيات إدارة إعدادات الموقع العامة',
                'color' => 'red',
                'category' => 'إعدادات النظام المتقدمة',
            ],
            'super::admin::setting' => [
                'label' => 'إعدادات السوبر أدمن',
                'icon' => 'heroicon-o-shield-exclamation',
                'description' => 'صلاحيات إدارة إعدادات السوبر أدمن',
                'color' => 'rose',
                'category' => 'إعدادات النظام المتقدمة',
            ],
            'language' => [
                'label' => 'اللغات',
                'icon' => 'heroicon-o-language',
                'description' => 'صلاحيات إدارة لغات النظام',
                'color' => 'pink',
                'category' => 'إعدادات النظام المتقدمة',
            ],
            'language::manager' => [
                'label' => 'مدير اللغات',
                'icon' => 'heroicon-o-cog-6-tooth',
                'description' => 'صلاحيات إدارة مدير اللغات والترجمات',
                'color' => 'violet',
                'category' => 'إعدادات النظام المتقدمة',
            ],
            'store' => [
                'label' => 'المتاجر',
                'icon' => 'heroicon-o-building-storefront',
                'description' => 'صلاحيات إدارة المتاجر',
                'color' => 'slate',
                'category' => 'إعدادات النظام المتقدمة',
            ],
            'home::slider' => [
                'label' => 'شرائح الصفحة الرئيسية',
                'icon' => 'heroicon-o-photo',
                'description' => 'صلاحيات إدارة شرائح الصفحة الرئيسية',
                'color' => 'indigo',
                'category' => 'إعدادات النظام المتقدمة',
            ],

            // و. الصفحات والودجات
            'page' => [
                'label' => 'الصفحات',
                'icon' => 'heroicon-o-document',
                'description' => 'صلاحيات الوصول للصفحات المخصصة',
                'color' => 'gray',
                'category' => 'الصفحات والودجات',
            ],
            'widget' => [
                'label' => 'الودجات',
                'icon' => 'heroicon-o-squares-plus',
                'description' => 'صلاحيات الوصول للودجات',
                'color' => 'zinc',
                'category' => 'الصفحات والودجات',
            ],
        ];

        // الصلاحيات المبسطة المسموحة فقط
        $allowedActions = ['view_any', 'create', 'update', 'delete', 'page', 'widget'];

        // تجميع الصلاحيات لكل مورد منفصل
        foreach ($resourceGroups as $resourceKey => $resourceData) {
            $resourcePermissions = [];

            foreach ($permissions as $permission) {
                $permissionName = $permission->name;

                // التحقق من أن الصلاحية تخص هذا المورد
                if (static::permissionBelongsToResource($permissionName, $resourceKey)) {
                    // التحقق من أن الصلاحية من الصلاحيات الأساسية المسموحة
                    if (static::isAllowedPermission($permissionName, $allowedActions)) {
                        $resourcePermissions[$permission->id] = static::translatePermissionName($permissionName);
                    }
                }
            }

            // إضافة المورد فقط إذا كان له صلاحيات
            if (!empty($resourcePermissions)) {
                $groupedPermissions[$resourceKey] = [
                    'label' => $resourceData['label'],
                    'icon' => $resourceData['icon'],
                    'description' => $resourceData['description'],
                    'color' => $resourceData['color'],
                    'category' => $resourceData['category'],
                    'permissions' => $resourcePermissions,
                ];
            }
        }

        return $groupedPermissions;
    }

    /**
     * التحقق من أن الصلاحية تخص مورد معين
     */
    protected static function permissionBelongsToResource(string $permissionName, string $resourceKey): bool
    {
        // التحقق من أن اسم الصلاحية يحتوي على اسم المورد
        return str_contains($permissionName, $resourceKey);
    }

    /**
     * التحقق من أن الصلاحية من الصلاحيات المسموحة
     */
    protected static function isAllowedPermission(string $permissionName, array $allowedActions): bool
    {
        foreach ($allowedActions as $action) {
            // للصلاحيات المبسطة مثل view_any_user, create_user, etc.
            if (str_starts_with($permissionName, $action . '_')) {
                return true;
            }
            // للصفحات والودجات مثل page_DashboardPage, widget_StatsOverview
            if (str_starts_with($permissionName, $action)) {
                return true;
            }
        }

        return false;
    }
}
