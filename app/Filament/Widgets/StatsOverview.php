<?php

namespace App\Filament\Widgets;

use App\Models\Appointment;
use App\Models\Category;
use App\Models\MetalPrice;
use App\Models\Order;
use App\Models\Product;
use App\Models\Store;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Traits\HasWidgetPermissionFiltering;

class StatsOverview extends BaseWidget
{
    use HasWidgetPermissionFiltering;

    protected static ?int $sort = 0;

    protected function getStats(): array
    {
        // Get today's gold price
        $goldPrice = MetalPrice::where('metal_type', 'gold')
            ->where('purity', '24K')
            ->where('currency', 'EGP')
            ->whereDate('price_date', now())
            ->first();

        $goldPriceValue = $goldPrice ? number_format($goldPrice->price_per_gram, 2) . ' ج.م' : 'غير متوفر';

        // Get yesterday's gold price for comparison
        $yesterdayGoldPrice = MetalPrice::where('metal_type', 'gold')
            ->where('purity', '24K')
            ->where('currency', 'EGP')
            ->whereDate('price_date', now()->subDay())
            ->first();

        $goldPriceChange = 0;
        if ($goldPrice && $yesterdayGoldPrice) {
            $goldPriceChange = (($goldPrice->price_per_gram - $yesterdayGoldPrice->price_per_gram) / $yesterdayGoldPrice->price_per_gram) * 100;
        }

        // Get total sales
        $totalSales = Order::where('status', 'completed')
            ->where('payment_status', 'paid')
            ->sum('total_amount');

        // Get sales for this month
        $thisMonthSales = Order::where('status', 'completed')
            ->where('payment_status', 'paid')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('total_amount');

        // Get sales for last month
        $lastMonthSales = Order::where('status', 'completed')
            ->where('payment_status', 'paid')
            ->whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->sum('total_amount');

        $salesChange = 0;
        if ($lastMonthSales > 0) {
            $salesChange = (($thisMonthSales - $lastMonthSales) / $lastMonthSales) * 100;
        }

        // Get orders counts by status
        $pendingOrders = Order::where('status', 'pending')->count();
        $processingOrders = Order::where('status', 'processing')->count();
        $shippedOrders = Order::where('status', 'shipped')->count();
        $deliveredOrders = Order::where('status', 'delivered')->count();
        $cancelledOrders = Order::where('status', 'cancelled')->count();

        // Get total orders count
        $totalOrders = Order::count();

        // Get orders count for today
        $todayOrders = Order::whereDate('created_at', now())->count();

        // Get upcoming appointments count
        $upcomingAppointments = Appointment::where('status', 'confirmed')
            ->where('appointment_date', '>=', now())
            ->count();

        // Get today's appointments
        $todayAppointments = Appointment::where('status', 'confirmed')
            ->whereDate('appointment_date', now())
            ->count();

        // Get total products count
        $totalProducts = Product::count();
        $activeProducts = Product::where('is_active', true)->count();
        $featuredProducts = Product::where('is_featured', true)->count();

        // Get total categories count
        $totalCategories = Category::count();

        // Get total stores count
        $totalStores = Store::count();

        // Get total customers count
        $totalCustomers = User::role('user')->count();

        // Get new customers this month
        $newCustomers = User::role('user')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        return [
            Stat::make('سعر الذهب عيار 24', $goldPriceValue)
                ->description($goldPriceChange >= 0 ? 'ارتفاع بنسبة ' . number_format(abs($goldPriceChange), 2) . '%' : 'انخفاض بنسبة ' . number_format(abs($goldPriceChange), 2) . '%')
                ->descriptionIcon($goldPriceChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($goldPriceChange >= 0 ? 'success' : 'danger')
                ->chart([7, 2, 10, 3, 15, 4, 17])
                ->extraAttributes([
                    'class' => 'cursor-pointer',
                    'wire:click' => "\$dispatch('open-modal', { id: 'gold-price-chart' })",
                ]),

            Stat::make('إجمالي المبيعات', number_format($totalSales, 2) . ' ج.م')
                ->description('مبيعات الشهر: ' . number_format($thisMonthSales, 2) . ' ج.م')
                ->descriptionIcon($salesChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($salesChange >= 0 ? 'success' : 'danger')
                ->chart([4, 7, 12, 9, 18, 13, 22])
                ->url(route('filament.admin.resources.orders.index')),

            Stat::make('إجمالي الطلبات', $totalOrders)
                ->description('طلبات اليوم: ' . $todayOrders)
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('primary')
                ->chart([3, 5, 7, 6, 9, 5, 10])
                ->url(route('filament.admin.resources.orders.index')),

            Stat::make('الطلبات قيد الانتظار', $pendingOrders)
                ->description('قيد المعالجة: ' . $processingOrders . ' | تم الشحن: ' . $shippedOrders)
                ->descriptionIcon('heroicon-m-truck')
                ->color('warning')
                ->chart([3, 5, 7, 6, 9, 5, 10])
                ->url(route('filament.admin.resources.orders.index', ['tableFilters[status][value]' => 'pending'])),

            Stat::make('المواعيد القادمة', $upcomingAppointments)
                ->description('مواعيد اليوم: ' . $todayAppointments)
                ->descriptionIcon('heroicon-m-calendar')
                ->color('info')
                ->chart([2, 4, 6, 5, 8, 4, 9])
                ->url(route('filament.admin.resources.appointments.index')),

            Stat::make('إجمالي المنتجات', $totalProducts)
                ->description('نشط: ' . $activeProducts . ' | مميز: ' . $featuredProducts)
                ->descriptionIcon('heroicon-m-cube')
                ->color('success')
                ->chart([10, 15, 20, 18, 25, 22, 30])
                ->url(route('filament.admin.resources.products.index')),

            Stat::make('إجمالي العملاء', $totalCustomers)
                ->description('عملاء جدد هذا الشهر: ' . $newCustomers)
                ->descriptionIcon('heroicon-m-users')
                ->color('primary')
                ->chart([5, 8, 12, 10, 15, 12, 18])
                ->url(route('filament.admin.resources.users.index')),

            Stat::make('إجمالي المتاجر', $totalStores)
                ->description('عدد الفئات: ' . $totalCategories)
                ->descriptionIcon('heroicon-m-building-storefront')
                ->color('warning')
                ->chart([5, 8, 12, 10, 15, 12, 18])
                ->url(route('filament.admin.resources.stores.index')),
        ];
    }
}
