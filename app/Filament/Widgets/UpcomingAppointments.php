<?php

namespace App\Filament\Widgets;

use App\Models\Appointment;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use App\Traits\HasWidgetPermissionFiltering;

class UpcomingAppointments extends BaseWidget
{
    use HasWidgetPermissionFiltering;

    protected static ?int $sort = 4;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $heading = 'المواعيد القادمة';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Appointment::query()
                    ->where('status', 'confirmed')
                    ->where('appointment_date', '>=', now())
                    ->orderBy('appointment_date')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('العميل')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('store_id')
                    ->label('رقم المتجر')
                    ->sortable(),

                Tables\Columns\TextColumn::make('appointment_date')
                    ->label('تاريخ ووقت الموعد')
                    ->dateTime('d-m-Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('service_type_ar')
                    ->label('نوع الخدمة')
                    ->searchable(),

                Tables\Columns\IconColumn::make('reminder_sent')
                    ->label('تم إرسال التذكير')
                    ->boolean(),

                Tables\Columns\TextColumn::make('confirmation_code')
                    ->label('رمز التأكيد')
                    ->searchable(),
            ])
            ->actions([
                Tables\Actions\Action::make('عرض')
                    ->url(fn (Appointment $record): string => route('filament.admin.resources.appointments.edit', $record))
                    ->icon('heroicon-m-eye'),
            ]);
    }
}
