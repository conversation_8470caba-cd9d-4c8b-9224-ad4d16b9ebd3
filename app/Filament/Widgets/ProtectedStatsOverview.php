<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Services\SuperAdminProtectionService;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class ProtectedStatsOverview extends BaseWidget
{
    protected static ?string $pollingInterval = '15s';

    protected function getStats(): array
    {
        return [
            Stat::make('إجمالي المستخدمين', $this->getFilteredUserCount())
                ->description('عدد المستخدمين المسجلين')
                ->descriptionIcon('heroicon-m-users')
                ->color('success'),

            Stat::make('الأدوار النشطة', $this->getFilteredRoleCount())
                ->description('عدد الأدوار المتاحة')
                ->descriptionIcon('heroicon-m-shield-check')
                ->color('info'),

            Stat::make('الصلاحيات', $this->getFilteredPermissionCount())
                ->description('عدد الصلاحيات المتاحة')
                ->descriptionIcon('heroicon-m-key')
                ->color('warning'),

            Stat::make('المستخدمين النشطين', $this->getActiveUserCount())
                ->description('المستخدمين المفعلين')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('primary'),
        ];
    }

    /**
     * الحصول على عدد المستخدمين المفلتر
     */
    protected function getFilteredUserCount(): int
    {
        return SuperAdminProtectionService::getFilteredUserCount();
    }

    /**
     * الحصول على عدد الأدوار المفلتر
     */
    protected function getFilteredRoleCount(): int
    {
        return SuperAdminProtectionService::getFilteredRoleCount();
    }

    /**
     * الحصول على عدد الصلاحيات المفلتر
     */
    protected function getFilteredPermissionCount(): int
    {
        return SuperAdminProtectionService::getFilteredPermissionCount();
    }

    /**
     * الحصول على عدد المستخدمين النشطين (مفلتر)
     */
    protected function getActiveUserCount(): int
    {
        $query = User::where('is_active', true);
        return SuperAdminProtectionService::filterUsers($query)->count();
    }

    /**
     * التحقق من إمكانية عرض الودجت
     */
    public static function canView(): bool
    {
        return auth()->check() && auth()->user()->can('view_any_user');
    }

    /**
     * ترتيب الودجت
     */
    protected static ?int $sort = 1;

    /**
     * عرض الودجت في عمود كامل
     */
    protected int | string | array $columnSpan = 'full';
}
