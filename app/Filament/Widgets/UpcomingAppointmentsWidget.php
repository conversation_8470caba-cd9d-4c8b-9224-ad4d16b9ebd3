<?php

namespace App\Filament\Widgets;

use App\Models\Appointment;
use App\Services\WidgetService;
use App\Traits\HasAdvancedWidgetPermissions;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class UpcomingAppointmentsWidget extends BaseWidget
{
    use HasAdvancedWidgetPermissions;

    protected static ?int $sort = 6;

    protected int | string | array $columnSpan = [
        'md' => 2,
        'xl' => 3,
    ];

    protected static ?string $pollingInterval = '60s';

    public static function canView(): bool
    {
        return parent::canView() && WidgetService::canViewWidget('view_any_appointment');
    }

    protected static function getRequiredPermission(): ?string
    {
        return 'view_any_appointment';
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Appointment::query()
                    ->with(['user', 'store'])
                    ->where('appointment_date', '>=', now())
                    ->where('status', 'confirmed')
                    ->orderBy('appointment_date')
                    ->limit(8)
            )
            ->heading('المواعيد القادمة')
            ->description('المواعيد المؤكدة القادمة')
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('العميل')
                    ->searchable()
                    ->default('غير محدد')
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('service_type')
                    ->label('الخدمة')
                    ->searchable()
                    ->default('غير محدد')
                    ->color('primary')
                    ->getStateUsing(fn ($record) => $record->service_type ?? 'غير محدد'),

                Tables\Columns\TextColumn::make('appointment_date')
                    ->label('التاريخ')
                    ->date('Y-m-d')
                    ->sortable()
                    ->badge()
                    ->color(fn ($record) =>
                        $record->appointment_date->isToday() ? 'warning' :
                        ($record->appointment_date->isTomorrow() ? 'primary' : 'gray')
                    ),

                Tables\Columns\TextColumn::make('appointment_time')
                    ->label('الوقت')
                    ->time('H:i')
                    ->badge()
                    ->color('success'),

                Tables\Columns\TextColumn::make('status')
                    ->label('الحالة')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'confirmed' => 'success',
                        'pending' => 'warning',
                        'cancelled' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'confirmed' => 'مؤكد',
                        'pending' => 'معلق',
                        'cancelled' => 'ملغي',
                        default => $state,
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('عرض')
                    ->icon('heroicon-o-eye')
                    ->color('primary')
                    ->size('sm')
                    ->url(fn (Appointment $record): string =>
                        $this->getResourceUrl('appointments') . "/{$record->id}"
                    )
                    ->openUrlInNewTab(),
            ])
            ->emptyStateHeading('لا توجد مواعيد قادمة')
            ->emptyStateDescription('لا توجد مواعيد مؤكدة في الفترة القادمة.')
            ->emptyStateIcon('heroicon-o-calendar')
            ->striped()
            ->paginated(false);
    }
}
