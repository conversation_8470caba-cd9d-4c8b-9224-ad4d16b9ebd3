<?php

namespace App\Filament\Widgets;

use App\Services\WidgetService;
use App\Traits\HasAdvancedWidgetPermissions;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ContentStatsWidget extends BaseWidget
{
    use HasAdvancedWidgetPermissions;

    protected static ?int $sort = 2;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $pollingInterval = '60s';

    public static function canView(): bool
    {
        return parent::canView() && (
            WidgetService::canViewWidget('view_any_blog::post') ||
            WidgetService::canViewWidget('view_any_page') ||
            WidgetService::canViewWidget('view_any_category') ||
            WidgetService::canViewWidget('view_any_feature') ||
            WidgetService::canViewWidget('view_any_testimonial') ||
            WidgetService::canViewWidget('view_any_home::slider')
        );
    }

    protected function getStats(): array
    {
        $stats = [];
        $contentStats = WidgetService::getContentStats();

        // إحصائيات الفئات
        if (WidgetService::canViewWidget('view_any_category')) {
            $stats[] = Stat::make('الفئات', $this->formatNumber($contentStats['categories']))
                ->description("نشطة: {$contentStats['active_categories']}")
                ->descriptionIcon($this->getWidgetIcon('categories'))
                ->color($this->getWidgetColor('content'))
                ->url($this->getResourceUrl('categories'));
        }

        // إحصائيات مقالات المدونة
        if (WidgetService::canViewWidget('view_any_blog::post')) {
            $stats[] = Stat::make('مقالات المدونة', $this->formatNumber($contentStats['blog_posts']))
                ->description("منشورة: {$contentStats['published_posts']}")
                ->descriptionIcon($this->getWidgetIcon('blog'))
                ->color($this->getWidgetColor('content'))
                ->url($this->getResourceUrl('blog_posts'));
        }

        // إحصائيات الصفحات
        if (WidgetService::canViewWidget('view_any_page')) {
            $stats[] = Stat::make('الصفحات', $this->formatNumber($contentStats['pages']))
                ->description("نشطة: {$contentStats['active_pages']}")
                ->descriptionIcon($this->getWidgetIcon('pages'))
                ->color($this->getWidgetColor('content'))
                ->url($this->getResourceUrl('pages'));
        }

        // إحصائيات الميزات
        if (WidgetService::canViewWidget('view_any_feature')) {
            $stats[] = Stat::make('الميزات', $this->formatNumber($contentStats['features']))
                ->description('ميزات الموقع')
                ->descriptionIcon($this->getWidgetIcon('features'))
                ->color($this->getWidgetColor('content'))
                ->url($this->getResourceUrl('features'));
        }

        // إحصائيات الشهادات
        if (WidgetService::canViewWidget('view_any_testimonial')) {
            $stats[] = Stat::make('الشهادات', $this->formatNumber($contentStats['testimonials']))
                ->description('شهادات العملاء')
                ->descriptionIcon($this->getWidgetIcon('testimonials'))
                ->color($this->getWidgetColor('content'))
                ->url($this->getResourceUrl('testimonials'));
        }

        // إحصائيات شرائح الصفحة الرئيسية
        if (WidgetService::canViewWidget('view_any_home::slider')) {
            $stats[] = Stat::make('شرائح الصفحة الرئيسية', $this->formatNumber($contentStats['home_sliders']))
                ->description('شرائح العرض')
                ->descriptionIcon($this->getWidgetIcon('sliders'))
                ->color($this->getWidgetColor('content'))
                ->url($this->getResourceUrl('home_sliders'));
        }

        return $stats;
    }

    /**
     * الحصول على الصلاحيات المطلوبة للموارد
     */
    public static function getRequiredResourcePermissions(): array
    {
        return [
            'view_any_category',
            'view_any_blog::post',
            'view_any_page',
            'view_any_feature',
            'view_any_testimonial',
            'view_any_home::slider',
        ];
    }
}
