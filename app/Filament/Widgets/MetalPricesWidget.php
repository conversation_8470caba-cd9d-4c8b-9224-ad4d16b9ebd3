<?php

namespace App\Filament\Widgets;

use App\Services\WidgetService;
use App\Traits\HasAdvancedWidgetPermissions;
use Filament\Widgets\Widget;

class MetalPricesWidget extends Widget
{
    use HasAdvancedWidgetPermissions;

    protected static string $view = 'filament.widgets.metal-prices-widget';

    protected static ?int $sort = 4;

    protected int | string | array $columnSpan = [
        'md' => 2,
        'xl' => 3,
    ];

    protected static ?string $pollingInterval = '30s';

    public static function canView(): bool
    {
        return parent::canView() && WidgetService::canViewWidget('view_any_metal::price');
    }

    protected static function getRequiredPermission(): ?string
    {
        return 'view_any_metal::price';
    }

    public function getViewData(): array
    {
        $metalStats = WidgetService::getMetalPricesStats();
        $latestPrices = $metalStats['latest_prices'];

        // تجميع الأسعار حسب نوع المعدن مع الأسماء العربية
        $groupedPrices = $this->groupPricesByMetal($latestPrices);

        return [
            'grouped_prices' => $groupedPrices,
            'total_prices' => $metalStats['total'],
            'active_prices' => $metalStats['active'],
            'today_updates' => $metalStats['today'],
            'resource_url' => $this->getResourceUrl('metal_prices'),
            'last_update' => $latestPrices->max('updated_at'),
        ];
    }

    /**
     * تجميع الأسعار حسب نوع المعدن مع الترجمة العربية
     */
    private function groupPricesByMetal($prices): array
    {
        $metalNames = [
            'gold' => 'الذهب',
            'silver' => 'الفضة',
            'platinum' => 'البلاتين',
            'palladium' => 'البلاديوم',
        ];

        $purityNames = [
            '24' => '24 قيراط',
            '22' => '22 قيراط',
            '21' => '21 قيراط',
            '18' => '18 قيراط',
            '14' => '14 قيراط',
            '999' => 'فضة خالصة',
            '925' => 'فضة استرليني',
            '950' => 'بلاتين 950',
            '900' => 'بلاتين 900',
        ];

        $grouped = [];

        foreach ($prices as $price) {
            $metalType = $price->metal_type;
            $metalNameAr = $metalNames[$metalType] ?? ucfirst($metalType);

            if (!isset($grouped[$metalType])) {
                $grouped[$metalType] = [
                    'name' => $metalNameAr,
                    'type' => $metalType,
                    'prices' => [],
                ];
            }

            $purityNameAr = $purityNames[$price->purity] ?? $price->purity . ' قيراط';

            $grouped[$metalType]['prices'][] = [
                'id' => $price->id,
                'purity' => $price->purity,
                'purity_name' => $purityNameAr,
                'price_per_gram' => $price->price_per_gram,
                'currency' => $price->currency,
                'updated_at' => $price->updated_at,
                'created_at' => $price->created_at,
            ];
        }

        // ترتيب المعادن (الذهب أولاً، ثم الفضة)
        $orderedGroups = [];
        $metalOrder = ['gold', 'silver', 'platinum', 'palladium'];

        foreach ($metalOrder as $metalType) {
            if (isset($grouped[$metalType])) {
                // ترتيب الأسعار داخل كل معدن من الأعلى إلى الأقل
                usort($grouped[$metalType]['prices'], function($a, $b) {
                    return $b['price_per_gram'] <=> $a['price_per_gram'];
                });
                $orderedGroups[$metalType] = $grouped[$metalType];
            }
        }

        return $orderedGroups;
    }

    /**
     * الحصول على الصلاحيات المطلوبة للموارد
     */
    public static function getRequiredResourcePermissions(): array
    {
        return [
            'view_any_metal::price',
        ];
    }
}
