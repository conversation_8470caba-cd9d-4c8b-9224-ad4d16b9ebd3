<?php

namespace App\Filament\Widgets;

use App\Services\WidgetService;
use App\Traits\HasAdvancedWidgetPermissions;
use Filament\Widgets\Widget;

class MetalPricesWidget extends Widget
{
    use HasAdvancedWidgetPermissions;

    protected static string $view = 'filament.widgets.metal-prices-widget';

    protected static ?int $sort = 4;

    protected int | string | array $columnSpan = [
        'md' => 2,
        'xl' => 3,
    ];

    protected static ?string $pollingInterval = '30s';

    public static function canView(): bool
    {
        return parent::canView() && WidgetService::canViewWidget('view_any_metal::price');
    }

    protected static function getRequiredPermission(): ?string
    {
        return 'view_any_metal::price';
    }

    public function getViewData(): array
    {
        $metalStats = WidgetService::getMetalPricesStats();

        return [
            'latest_prices' => $metalStats['latest_gold_prices'],
            'total_prices' => $metalStats['total'],
            'active_prices' => $metalStats['active'],
            'today_updates' => $metalStats['today'],
            'resource_url' => $this->getResourceUrl('metal_prices'),
        ];
    }

    /**
     * الحصول على الصلاحيات المطلوبة للموارد
     */
    public static function getRequiredResourcePermissions(): array
    {
        return [
            'view_any_metal::price',
        ];
    }
}
