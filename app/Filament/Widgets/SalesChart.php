<?php

namespace App\Filament\Widgets;

use App\Models\Order;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;
use App\Traits\HasWidgetPermissionFiltering;

class SalesChart extends ChartWidget
{
    use HasWidgetPermissionFiltering;

    protected static ?string $heading = 'مبيعات آخر 30 يوم';

    protected static ?int $sort = 1;

    protected function getData(): array
    {
        $data = $this->getSalesData();

        return [
            'datasets' => [
                [
                    'label' => 'المبيعات (ج.م)',
                    'data' => $data['sales'],
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                    'borderColor' => '#f59e0b',
                    'borderWidth' => 2,
                    'pointBackgroundColor' => '#d97706',
                    'pointBorderColor' => '#ffffff',
                    'pointHoverBackgroundColor' => '#ffffff',
                    'pointHoverBorderColor' => '#d97706',
                    'pointRadius' => 4,
                    'pointHoverRadius' => 6,
                    'tension' => 0.3,
                    'fill' => true,
                ],
            ],
            'labels' => $data['dates'],
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    private function getSalesData(): array
    {
        $startDate = now()->subDays(30);
        $endDate = now();

        $dates = [];
        $sales = [];

        for ($date = $startDate; $date->lte($endDate); $date->addDay()) {
            $dailySales = Order::where('status', 'completed')
                ->where('payment_status', 'paid')
                ->whereDate('created_at', $date->format('Y-m-d'))
                ->sum('total_amount');

            $dates[] = $date->format('d/m');
            $sales[] = $dailySales;
        }

        return [
            'dates' => $dates,
            'sales' => $sales,
        ];
    }
}
