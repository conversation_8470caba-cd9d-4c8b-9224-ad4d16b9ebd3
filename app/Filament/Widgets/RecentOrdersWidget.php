<?php

namespace App\Filament\Widgets;

use App\Models\Order;
use App\Services\SuperAdminProtectionService;
use App\Services\WidgetService;
use App\Traits\HasAdvancedWidgetPermissions;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class RecentOrdersWidget extends BaseWidget
{
    use HasAdvancedWidgetPermissions;

    protected static ?int $sort = 5;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $pollingInterval = '30s';

    public static function canView(): bool
    {
        return parent::canView() && WidgetService::canViewWidget('view_any_order');
    }

    protected static function getRequiredPermission(): ?string
    {
        return 'view_any_order';
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Order::query()
                    ->with(['user', 'items.product'])
                    ->latest()
                    ->limit(10)
            )
            ->heading('أحدث الطلبات')
            ->description('آخر 10 طلبات في النظام')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('رقم الطلب')
                    ->prefix('#')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('العميل')
                    ->searchable()
                    ->sortable()
                    ->default('غير محدد'),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('الحالة')
                    ->colors([
                        'warning' => 'pending',
                        'primary' => 'processing',
                        'success' => 'completed',
                        'danger' => 'cancelled',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'pending' => 'معلق',
                        'processing' => 'قيد المعالجة',
                        'completed' => 'مكتمل',
                        'cancelled' => 'ملغي',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('total_amount')
                    ->label('المبلغ الإجمالي')
                    ->money('EGP')
                    ->sortable(),

                Tables\Columns\TextColumn::make('items_count')
                    ->label('عدد المنتجات')
                    ->counts('items')
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الطلب')
                    ->dateTime('Y-m-d H:i')
                    ->sortable()
                    ->since()
                    ->tooltip(fn ($record) => $record->created_at->format('Y-m-d H:i:s')),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('عرض')
                    ->icon('heroicon-o-eye')
                    ->color('primary')
                    ->url(fn (Order $record): string => 
                        $this->getResourceUrl('orders') . "/{$record->id}"
                    )
                    ->openUrlInNewTab(),
            ])
            ->emptyStateHeading('لا توجد طلبات')
            ->emptyStateDescription('لم يتم إنشاء أي طلبات بعد.')
            ->emptyStateIcon('heroicon-o-shopping-cart')
            ->striped()
            ->paginated(false);
    }
}
