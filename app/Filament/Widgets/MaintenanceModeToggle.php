<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;
use App\Services\SettingsService;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Cache;
use App\Traits\HasWidgetPermissionFiltering;

class MaintenanceModeToggle extends Widget
{
    use HasWidgetPermissionFiltering;

    protected static string $view = 'filament.widgets.maintenance-mode-toggle';
    
    protected int | string | array $columnSpan = 'full';
    
    public bool $maintenanceMode = false;
    public string $maintenanceMessage = '';
    
    public function mount()
    {
        $settingsService = app(SettingsService::class);
        $this->maintenanceMode = $settingsService->get('maintenance_mode', false);
        $this->maintenanceMessage = $settingsService->get('maintenance_message', 'الموقع قيد الصيانة حالياً، يرجى العودة لاحقاً.');
    }
    
    public function toggleMaintenanceMode()
    {
        $settingsService = app(SettingsService::class);
        
        // Toggle maintenance mode
        $newStatus = !$this->maintenanceMode;
        $settingsService->set('maintenance_mode', $newStatus);
        
        // Clear cache
        Cache::forget('site_settings');
        
        // Update local property
        $this->maintenanceMode = $newStatus;
        
        // Show notification
        Notification::make()
            ->title($newStatus ? 'تم تفعيل وضع الصيانة' : 'تم إلغاء تفعيل وضع الصيانة')
            ->success()
            ->send();
    }
    
    public function updateMaintenanceMessage()
    {
        $settingsService = app(SettingsService::class);
        
        // Update maintenance message
        $settingsService->set('maintenance_message', $this->maintenanceMessage);
        
        // Clear cache
        Cache::forget('site_settings');
        
        // Show notification
        Notification::make()
            ->title('تم تحديث رسالة الصيانة')
            ->success()
            ->send();
    }
}
