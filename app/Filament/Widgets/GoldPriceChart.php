<?php

namespace App\Filament\Widgets;

use App\Models\MetalPrice;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;
use App\Traits\HasWidgetPermissionFiltering;

class GoldPriceChart extends ChartWidget
{
    use HasWidgetPermissionFiltering;

    protected static ?string $heading = 'أسعار الذهب عيار 24 (آخر 30 يوم)';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $data = $this->getGoldPriceData();

        return [
            'datasets' => [
                [
                    'label' => 'سعر الجرام (ج.م)',
                    'data' => $data['prices'],
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                    'borderColor' => '#f59e0b',
                    'borderWidth' => 2,
                    'pointBackgroundColor' => '#d97706',
                    'pointBorderColor' => '#ffffff',
                    'pointHoverBackgroundColor' => '#ffffff',
                    'pointHoverBorderColor' => '#d97706',
                    'pointRadius' => 4,
                    'pointHoverRadius' => 6,
                    'tension' => 0.3,
                    'fill' => true,
                ],
            ],
            'labels' => $data['dates'],
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    private function getGoldPriceData(): array
    {
        $startDate = now()->subDays(30);
        $endDate = now();

        $dates = [];
        $prices = [];

        $goldPrices = MetalPrice::where('metal_type', 'gold')
            ->where('purity', '24K')
            ->where('currency', 'EGP')
            ->whereBetween('price_date', [$startDate, $endDate])
            ->orderBy('price_date')
            ->get();

        $pricesByDate = [];
        foreach ($goldPrices as $price) {
            $date = Carbon::parse($price->price_date)->format('Y-m-d');
            $pricesByDate[$date] = $price->price_per_gram;
        }

        for ($date = $startDate; $date->lte($endDate); $date->addDay()) {
            $dateStr = $date->format('Y-m-d');
            $dateLabel = $date->format('d/m');

            $dates[] = $dateLabel;

            if (isset($pricesByDate[$dateStr])) {
                $prices[] = $pricesByDate[$dateStr];
            } else {
                // Use the previous day's price if available, otherwise use null
                $prevDate = $date->copy()->subDay()->format('Y-m-d');
                $prices[] = $pricesByDate[$prevDate] ?? null;
            }
        }

        return [
            'dates' => $dates,
            'prices' => $prices,
        ];
    }
}
