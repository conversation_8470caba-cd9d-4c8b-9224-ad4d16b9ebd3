<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Traits\HasPagePermissionFiltering;
use App\Services\FilamentPermissionService;

class DashboardPage extends Page
{
    use HasPagePermissionFiltering;

    protected static ?string $slug = 'dashboard';

    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static ?string $navigationLabel = 'لوحة التحكم';

    protected static ?string $title = 'لوحة التحكم';

    protected static ?int $navigationSort = -2;

    protected static string $view = 'filament.pages.dashboard';

    protected function getHeaderWidgets(): array
    {
        $widgets = [
            \App\Filament\Widgets\MaintenanceModeToggle::class,
            \App\Filament\Widgets\StatsOverview::class,
        ];

        return FilamentPermissionService::filterWidgets($widgets);
    }

    protected function getFooterWidgets(): array
    {
        $widgets = [
            \App\Filament\Widgets\SalesChart::class,
            \App\Filament\Widgets\GoldPriceChart::class,
            \App\Filament\Widgets\LatestOrders::class,
            \App\Filament\Widgets\UpcomingAppointments::class,
        ];

        return FilamentPermissionService::filterWidgets($widgets);
    }
}
