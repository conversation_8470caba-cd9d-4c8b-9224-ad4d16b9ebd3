<?php

namespace App\Services;

use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Collection;

class FilamentPermissionService
{
    /**
     * الحصول على الصلاحيات المتاحة للمستخدم الحالي
     */
    public static function getAvailablePermissions(): Collection
    {
        $currentUser = Auth::user();

        // إذا كان المستخدم super_admin، يمكنه رؤية جميع الصلاحيات
        if ($currentUser && $currentUser->hasRole('super_admin')) {
            return Permission::all();
        }

        // إذا كان المستخدم admin أو أي دور آخر، يرى فقط الصلاحيات التي يملكها
        if ($currentUser) {
            // جلب جميع الصلاحيات التي يملكها المستخدم الحالي عبر الأدوار
            $userRoles = $currentUser->roles;
            $userPermissions = collect();

            foreach ($userRoles as $role) {
                $userPermissions = $userPermissions->merge($role->permissions);
            }

            // إضافة الصلاحيات المباشرة للمستخدم
            $userPermissions = $userPermissions->merge($currentUser->permissions);

            // إزالة التكرار
            $userPermissions = $userPermissions->unique('id');

            return $userPermissions;
        }

        // إذا لم يكن هناك مستخدم مسجل دخول، إرجاع مجموعة فارغة
        return collect();
    }

    /**
     * التحقق من صلاحية الوصول لمورد معين
     */
    public static function canAccessResource(string $resourceClass): bool
    {
        $currentUser = Auth::user();

        if (!$currentUser) {
            return false;
        }

        // Super Admin يمكنه الوصول لكل شيء
        if ($currentUser->hasRole('super_admin')) {
            return true;
        }

        // استخراج اسم المورد من الكلاس
        $resourceName = static::extractResourceName($resourceClass);

        // التحقق من صلاحية view_any للمورد
        $permission = "view_any_{$resourceName}";

        return $currentUser->can($permission);
    }

    /**
     * التحقق من صلاحية الوصول لصفحة معينة
     */
    public static function canAccessPage(string $pageClass): bool
    {
        $currentUser = Auth::user();

        if (!$currentUser) {
            return false;
        }

        // Super Admin يمكنه الوصول لكل شيء
        if ($currentUser->hasRole('super_admin')) {
            return true;
        }

        // استخراج اسم الصفحة من الكلاس
        $pageName = static::extractPageName($pageClass);

        // التحقق من صلاحية الصفحة
        $permission = "page_{$pageName}";

        return $currentUser->can($permission);
    }

    /**
     * التحقق من صلاحية الوصول لودجة معينة
     */
    public static function canAccessWidget(string $widgetClass): bool
    {
        $currentUser = Auth::user();

        if (!$currentUser) {
            return false;
        }

        // Super Admin يمكنه الوصول لكل شيء
        if ($currentUser->hasRole('super_admin')) {
            return true;
        }

        // استخراج اسم الودجة من الكلاس
        $widgetName = static::extractWidgetName($widgetClass);

        // التحقق من صلاحية الودجة
        $permission = "widget_{$widgetName}";

        return $currentUser->can($permission);
    }

    /**
     * استخراج اسم المورد من الكلاس
     */
    public static function extractResourceName(string $resourceClass): string
    {
        // مثال: App\Filament\Resources\UserResource -> user
        $className = class_basename($resourceClass);
        $resourceName = str_replace('Resource', '', $className);

        // تحويل إلى snake_case
        $resourceName = strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $resourceName));

        // معالجة الحالات الخاصة
        $specialCases = [
            'blog_post' => 'blog::post',
            'job_application' => 'job::application',
            'language_manager' => 'language::manager',
            'metal_price' => 'metal::price',
            'metal_purity' => 'metal::purity',
            'metal_type' => 'metal::type',
            'site_setting' => 'site::setting',
            'super_admin_setting' => 'super::admin::setting',
            'setting_change' => 'setting::change',
            'home_slider' => 'home::slider',
        ];

        return $specialCases[$resourceName] ?? $resourceName;
    }

    /**
     * استخراج اسم الصفحة من الكلاس
     */
    public static function extractPageName(string $pageClass): string
    {
        // مثال: App\Filament\Pages\DashboardPage -> DashboardPage
        $className = class_basename($pageClass);

        return $className;
    }

    /**
     * استخراج اسم الودجة من الكلاس
     */
    public static function extractWidgetName(string $widgetClass): string
    {
        // مثال: App\Filament\Widgets\StatsOverview -> StatsOverview
        $className = class_basename($widgetClass);

        return $className;
    }

    /**
     * فلترة قائمة الموارد حسب الصلاحيات
     */
    public static function filterResources(array $resources): array
    {
        return array_filter($resources, function ($resource) {
            return static::canAccessResource($resource);
        });
    }

    /**
     * فلترة قائمة الصفحات حسب الصلاحيات
     */
    public static function filterPages(array $pages): array
    {
        return array_filter($pages, function ($page) {
            return static::canAccessPage($page);
        });
    }

    /**
     * فلترة قائمة الودجات حسب الصلاحيات
     */
    public static function filterWidgets(array $widgets): array
    {
        return array_filter($widgets, function ($widget) {
            return static::canAccessWidget($widget);
        });
    }

    /**
     * التحقق من صلاحية عنصر التنقل
     */
    public static function canAccessNavigationItem(string $url, ?string $permission = null): bool
    {
        $currentUser = Auth::user();

        if (!$currentUser) {
            return false;
        }

        // Super Admin يمكنه الوصول لكل شيء
        if ($currentUser->hasRole('super_admin')) {
            return true;
        }

        // إذا تم تحديد صلاحية معينة، التحقق منها
        if ($permission) {
            return $currentUser->can($permission);
        }

        // السماح بالوصول للروابط العامة (مثل الموقع الأمامي)
        if (str_starts_with($url, 'http') || $url === '/') {
            return true;
        }

        return true;
    }

    /**
     * الحصول على قائمة الموارد المتاحة للمستخدم الحالي
     */
    public static function getAvailableResourceClasses(): array
    {
        $allResources = [
            \App\Filament\Resources\UserResource::class,
            \App\Filament\Resources\RoleResource::class,
            \App\Filament\Resources\PermissionResource::class,
            \App\Filament\Resources\ProductResource::class,
            \App\Filament\Resources\CategoryResource::class,
            \App\Filament\Resources\OrderResource::class,
            \App\Filament\Resources\AppointmentResource::class,
            \App\Filament\Resources\BlogPostResource::class,
            \App\Filament\Resources\PageResource::class,
            \App\Filament\Resources\FeatureResource::class,
            \App\Filament\Resources\TestimonialResource::class,
            \App\Filament\Resources\HomeSliderResource::class,
            \App\Filament\Resources\StoreResource::class,
            \App\Filament\Resources\JobResource::class,
            \App\Filament\Resources\JobApplicationResource::class,
            \App\Filament\Resources\NewsletterResource::class,
            \App\Filament\Resources\MetalTypeResource::class,
            \App\Filament\Resources\MetalPurityResource::class,
            \App\Filament\Resources\MetalPriceResource::class,
            \App\Filament\Resources\SiteSettingResource::class,
            \App\Filament\Resources\SuperAdminSettingResource::class,
            \App\Filament\Resources\SettingChangeResource::class,
            \App\Filament\Resources\LanguageResource::class,
            \App\Filament\Resources\LanguageManagerResource::class,
        ];

        return static::filterResources($allResources);
    }

    /**
     * الحصول على قائمة الصفحات المتاحة للمستخدم الحالي
     */
    public static function getAvailablePageClasses(): array
    {
        $allPages = [
            \App\Filament\Pages\DashboardPage::class,
            \App\Filament\Pages\PreviewSettings::class,
            \App\Filament\Pages\SearchSettings::class,
            \App\Filament\Pages\SiteSettingsManager::class,
            \App\Filament\Pages\TranslationsManager::class,
        ];

        return static::filterPages($allPages);
    }

    /**
     * الحصول على قائمة الودجات المتاحة للمستخدم الحالي
     */
    public static function getAvailableWidgetClasses(): array
    {
        $allWidgets = [
            \App\Filament\Widgets\StatsOverview::class,
            \App\Filament\Widgets\SalesChart::class,
            \App\Filament\Widgets\GoldPriceChart::class,
            \App\Filament\Widgets\LatestOrders::class,
            \App\Filament\Widgets\UpcomingAppointments::class,
            \App\Filament\Widgets\MaintenanceModeToggle::class,
        ];

        return static::filterWidgets($allWidgets);
    }
}
