<?php

namespace App\Services;

use App\Models\User;
use App\Models\Product;
use App\Models\Category;
use App\Models\Order;
use App\Models\Appointment;
use App\Models\MetalPrice;
use App\Models\BlogPost;
use App\Models\Page;
use App\Models\Feature;
use App\Models\Testimonial;
use App\Models\Job;
use App\Models\JobApplication;
use App\Models\Newsletter;
use App\Models\Store;
use App\Models\HomeSlider;
use App\Models\Review;
use App\Models\Wishlist;
use App\Models\Cart;
use App\Models\Address;
use App\Models\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class WidgetService
{
    /**
     * الحصول على إحصائيات المستخدمين
     */
    public static function getUsersStats(): array
    {
        return Cache::remember('widget_users_stats', 300, function () {
            $totalUsers = User::count();
            $activeUsers = User::where('is_active', true)->count();
            $newUsersThisMonth = User::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count();
            $lastMonthUsers = User::whereMonth('created_at', now()->subMonth()->month)
                ->whereYear('created_at', now()->subMonth()->year)
                ->count();

            $growth = $lastMonthUsers > 0
                ? round((($newUsersThisMonth - $lastMonthUsers) / $lastMonthUsers) * 100, 1)
                : 0;

            return [
                'total' => $totalUsers,
                'active' => $activeUsers,
                'new_this_month' => $newUsersThisMonth,
                'growth_percentage' => $growth,
                'growth_trend' => $growth >= 0 ? 'up' : 'down',
            ];
        });
    }

    /**
     * الحصول على إحصائيات المنتجات
     */
    public static function getProductsStats(): array
    {
        return Cache::remember('widget_products_stats', 300, function () {
            $totalProducts = Product::count();
            $activeProducts = Product::where('is_active', true)->count();
            $featuredProducts = Product::where('is_featured', true)->count();
            $outOfStockProducts = Product::where('stock_quantity', '<=', 0)->count();
            $lowStockProducts = Product::whereBetween('stock_quantity', [1, 10])->count();

            return [
                'total' => $totalProducts,
                'active' => $activeProducts,
                'featured' => $featuredProducts,
                'out_of_stock' => $outOfStockProducts,
                'low_stock' => $lowStockProducts,
                'stock_percentage' => $totalProducts > 0
                    ? round((($totalProducts - $outOfStockProducts) / $totalProducts) * 100, 1)
                    : 0,
            ];
        });
    }

    /**
     * الحصول على إحصائيات الطلبات
     */
    public static function getOrdersStats(): array
    {
        return Cache::remember('widget_orders_stats', 300, function () {
            $totalOrders = Order::count();
            $pendingOrders = Order::where('status', 'pending')->count();
            $completedOrders = Order::where('status', 'completed')->count();
            $todayOrders = Order::whereDate('created_at', today())->count();
            $thisMonthOrders = Order::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count();
            $lastMonthOrders = Order::whereMonth('created_at', now()->subMonth()->month)
                ->whereYear('created_at', now()->subMonth()->year)
                ->count();

            $growth = $lastMonthOrders > 0
                ? round((($thisMonthOrders - $lastMonthOrders) / $lastMonthOrders) * 100, 1)
                : 0;

            $totalRevenue = Order::where('status', 'completed')->sum('total_amount');

            return [
                'total' => $totalOrders,
                'pending' => $pendingOrders,
                'completed' => $completedOrders,
                'today' => $todayOrders,
                'this_month' => $thisMonthOrders,
                'growth_percentage' => $growth,
                'growth_trend' => $growth >= 0 ? 'up' : 'down',
                'total_revenue' => $totalRevenue,
            ];
        });
    }

    /**
     * الحصول على إحصائيات المواعيد
     */
    public static function getAppointmentsStats(): array
    {
        return Cache::remember('widget_appointments_stats', 300, function () {
            $totalAppointments = Appointment::count();
            $pendingAppointments = Appointment::where('status', 'pending')->count();
            $confirmedAppointments = Appointment::where('status', 'confirmed')->count();
            $todayAppointments = Appointment::whereDate('appointment_date', today())->count();
            $upcomingAppointments = Appointment::where('appointment_date', '>', now())
                ->where('status', 'confirmed')
                ->count();

            return [
                'total' => $totalAppointments,
                'pending' => $pendingAppointments,
                'confirmed' => $confirmedAppointments,
                'today' => $todayAppointments,
                'upcoming' => $upcomingAppointments,
            ];
        });
    }

    /**
     * الحصول على إحصائيات أسعار المعادن
     */
    public static function getMetalPricesStats(): array
    {
        return Cache::remember('widget_metal_prices_stats', 300, function () {
            $totalPrices = MetalPrice::count();
            $activePrices = MetalPrice::where('is_active', true)->count();
            $todayPrices = MetalPrice::whereDate('created_at', today())->count();

            // أحدث أسعار جميع المعادن (آخر سعر لكل عيار)
            $latestPrices = MetalPrice::where('is_active', true)
                ->select('*')
                ->whereIn('id', function($query) {
                    $query->select(DB::raw('MAX(id)'))
                        ->from('metal_prices')
                        ->where('is_active', true)
                        ->groupBy(['metal_type', 'purity']);
                })
                ->orderBy('price_per_gram', 'desc')
                ->get();

            return [
                'total' => $totalPrices,
                'active' => $activePrices,
                'today' => $todayPrices,
                'latest_prices' => $latestPrices,
            ];
        });
    }

    /**
     * الحصول على إحصائيات المحتوى
     */
    public static function getContentStats(): array
    {
        return Cache::remember('widget_content_stats', 300, function () {
            return [
                'blog_posts' => BlogPost::count(),
                'published_posts' => BlogPost::whereNotNull('published_at')->count(),
                'pages' => Page::count(),
                'active_pages' => Page::where('is_active', true)->count(),
                'categories' => Category::count(),
                'active_categories' => Category::where('is_active', true)->count(),
                'features' => Feature::count(),
                'testimonials' => Testimonial::count(),
                'home_sliders' => HomeSlider::count(),
            ];
        });
    }

    /**
     * الحصول على إحصائيات الوظائف
     */
    public static function getJobsStats(): array
    {
        return Cache::remember('widget_jobs_stats', 300, function () {
            $totalJobs = Job::count();
            $activeJobs = Job::where('is_active', true)->count();
            $applications = JobApplication::count();
            $pendingApplications = JobApplication::where('status', 'pending')->count();

            return [
                'total_jobs' => $totalJobs,
                'active_jobs' => $activeJobs,
                'total_applications' => $applications,
                'pending_applications' => $pendingApplications,
            ];
        });
    }

    /**
     * الحصول على إحصائيات النشرة البريدية والتفاعل
     */
    public static function getEngagementStats(): array
    {
        return Cache::remember('widget_engagement_stats', 300, function () {
            return [
                'newsletter_subscribers' => Newsletter::count(),
                'reviews' => Review::count(),
                'average_rating' => Review::avg('rating') ?: 0,
                'wishlists' => Wishlist::count(),
                'active_carts' => Cart::whereHas('items')->count(),
                'stores' => Store::count(),
                'notifications' => Notification::where('read_at', null)->count(),
            ];
        });
    }

    /**
     * التحقق من صلاحية المستخدم لرؤية ويدجت معين
     */
    public static function canViewWidget(string $permission): bool
    {
        if (!Auth::check()) {
            return false;
        }

        // Super Admin يرى كل شيء
        if (SuperAdminProtectionService::isSuperAdmin()) {
            return true;
        }

        // فحص الصلاحية المحددة
        return Auth::user()->can($permission);
    }

    /**
     * مسح الكاش للويدجت
     */
    public static function clearWidgetCache(): void
    {
        $cacheKeys = [
            'widget_users_stats',
            'widget_products_stats',
            'widget_orders_stats',
            'widget_appointments_stats',
            'widget_metal_prices_stats',
            'widget_content_stats',
            'widget_jobs_stats',
            'widget_engagement_stats',
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
    }
}
