<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        \App\Models\User::class => \App\Policies\UserPolicy::class,
        \App\Models\Product::class => \App\Policies\ProductPolicy::class,
        \App\Models\Category::class => \App\Policies\CategoryPolicy::class,
        \App\Models\Order::class => \App\Policies\OrderPolicy::class,
        \App\Models\Appointment::class => \App\Policies\AppointmentPolicy::class,
        \App\Models\BlogPost::class => \App\Policies\BlogPostPolicy::class,
        \App\Models\Page::class => \App\Policies\PagePolicy::class,
        \App\Models\Feature::class => \App\Policies\FeaturePolicy::class,
        \App\Models\Testimonial::class => \App\Policies\TestimonialPolicy::class,
        \App\Models\HomeSlider::class => \App\Policies\HomeSliderPolicy::class,
        \App\Models\Store::class => \App\Policies\StorePolicy::class,
        \App\Models\Job::class => \App\Policies\JobPolicy::class,
        \App\Models\JobApplication::class => \App\Policies\JobApplicationPolicy::class,
        \App\Models\Newsletter::class => \App\Policies\NewsletterPolicy::class,
        \App\Models\MetalType::class => \App\Policies\MetalTypePolicy::class,
        \App\Models\MetalPurity::class => \App\Policies\MetalPurityPolicy::class,
        \App\Models\MetalPrice::class => \App\Policies\MetalPricePolicy::class,
        \App\Models\SiteSetting::class => \App\Policies\SiteSettingPolicy::class,
        \App\Models\SuperAdminSetting::class => \App\Policies\SuperAdminSettingPolicy::class,
        \App\Models\SettingChange::class => \App\Policies\SettingChangePolicy::class,
        \App\Models\Language::class => \App\Policies\LanguagePolicy::class,
        \Spatie\Permission\Models\Role::class => \App\Policies\RolePolicy::class,
        \Spatie\Permission\Models\Permission::class => \App\Policies\PermissionPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // تسجيل الـ Policies
        $this->registerPolicies();

        // تعريف Gate للـ Super Admin
        Gate::before(function ($user, $ability) {
            // إذا كان المستخدم لديه دور super_admin، يُسمح له بكل شيء
            if ($user->hasRole('super_admin')) {
                return true;
            }
        });

        // تعريف Gate مخصص للـ Super Admin
        Gate::define('super_admin', function ($user) {
            return $user->hasRole('super_admin');
        });

        // تعريف Gate للـ Panel User
        Gate::define('panel_user', function ($user) {
            return $user->hasAnyRole(['super_admin', 'admin', 'manager']);
        });
    }
}
