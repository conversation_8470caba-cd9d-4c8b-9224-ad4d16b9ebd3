<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use <PERSON><PERSON>han<PERSON>alleh\FilamentShield\FilamentShieldPlugin;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use App\Services\FilamentPermissionService;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->login()
            ->colors([
                'primary' => Color::Amber,
            ])
            ->brandName(function () {
                $settings = app(\App\Services\SettingsService::class)->all();
                return $settings->site_name ?? 'مكة جولد';
            })
            ->brandLogo(function () {
                $settings = app(\App\Services\SettingsService::class)->all();
                return $settings->logo ? asset('storage/' . $settings->logo) : asset('images/logo.png');
            })
            ->favicon(function () {
                $settings = app(\App\Services\SettingsService::class)->all();
                return $settings->favicon ? asset('storage/' . $settings->favicon) : asset('favicon.ico');
            })
            ->resources(FilamentPermissionService::getAvailableResourceClasses())
            ->pages(array_merge(
                [\App\Filament\Pages\DashboardPage::class],
                FilamentPermissionService::getAvailablePageClasses()
            ))
            ->widgets(array_merge(
                [Widgets\AccountWidget::class],
                FilamentPermissionService::getAvailableWidgetClasses()
            ))
            ->navigationGroups([
                'إدارة النظام الأساسية',
                'إدارة المحتوى والمنتجات',
                'إدارة العمليات التجارية',
                'إدارة المعادن المتخصصة',
                'إدارة الموقع والمتاجر',
                'إدارة الموارد البشرية',
                'التسويق والتواصل',
                'إعدادات النظام المتقدمة',
            ])
            ->navigationItems([
                \Filament\Navigation\NavigationItem::make('الموقع الأمامي')
                    ->url('/')
                    ->icon('heroicon-o-globe-alt')
                    ->sort(100)
                    ->visible(fn () => FilamentPermissionService::canAccessNavigationItem('/')),
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->plugins([
                FilamentShieldPlugin::make(),
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->databaseNotifications()
            ->sidebarCollapsibleOnDesktop();
    }
}
