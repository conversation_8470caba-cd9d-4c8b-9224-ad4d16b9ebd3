<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Filament\Facades\Filament;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationItem;

class FilamentRolesServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تخصيص مجموعة التنقل للأدوار والصلاحيات
        Filament::serving(function () {
            // إضافة أيقونات مخصصة للمجموعات
            Filament::registerNavigationGroups([
                NavigationGroup::make('إدارة الصلاحيات')
                    ->icon('heroicon-o-shield-check')
                    ->collapsed(false),
            ]);

            // تخصيص عناصر التنقل
            Filament::registerNavigationItems([
                NavigationItem::make('دليل الأدوار')
                    ->url('/admin/roles-guide')
                    ->icon('heroicon-o-book-open')
                    ->group('إدارة الصلاحيات')
                    ->sort(3)
                    ->visible(fn () => auth()->user()?->hasRole(['super_admin', 'admin'])),
            ]);
        });

        // تخصيص ألوان النظام
        $this->customizeTheme();
    }

    /**
     * تخصيص ألوان وتصميم النظام
     */
    protected function customizeTheme(): void
    {
        // يمكن إضافة تخصيصات إضافية هنا
    }
}
