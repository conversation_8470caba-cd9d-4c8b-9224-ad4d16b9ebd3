<?php

namespace App\Traits;

use App\Services\SuperAdminProtectionService;
use App\Services\WidgetService;
use Illuminate\Support\Facades\Auth;

trait HasAdvancedWidgetPermissions
{
    /**
     * التحقق من إمكانية عرض الويدجت
     */
    public static function canView(): bool
    {
        if (!Auth::check()) {
            return false;
        }

        // Super Admin يرى جميع الويدجت
        if (SuperAdminProtectionService::isSuperAdmin()) {
            return true;
        }

        // فحص الصلاحية المخصصة للويدجت أولاً
        $widgetPermission = static::getWidgetPermission();
        if ($widgetPermission && !Auth::user()->can($widgetPermission)) {
            return false;
        }

        // فحص الصلاحية المطلوبة للمورد
        $permission = static::getRequiredPermission();
        if ($permission && !Auth::user()->can($permission)) {
            return false;
        }

        return true;
    }

    /**
     * الحصول على صلاحية الويدجت المخصصة
     */
    protected static function getWidgetPermission(): ?string
    {
        $className = class_basename(static::class);
        return "widget_{$className}";
    }

    /**
     * الحصول على الصلاحية المطلوبة للويدجت
     * يجب تعريف هذه الدالة في كل ويدجت
     */
    protected static function getRequiredPermission(): ?string
    {
        return null;
    }

    /**
     * الحصول على لون الويدجت حسب النوع
     */
    protected function getWidgetColor(string $type = 'primary'): string
    {
        return match($type) {
            'users' => 'info',
            'products' => 'success',
            'orders' => 'warning',
            'appointments' => 'primary',
            'content' => 'gray',
            'jobs' => 'indigo',
            'engagement' => 'pink',
            'metal_prices' => 'amber',
            'system' => 'red',
            default => 'primary',
        };
    }

    /**
     * الحصول على أيقونة الويدجت حسب النوع
     */
    protected function getWidgetIcon(string $type): string
    {
        return match($type) {
            'users' => 'heroicon-o-users',
            'products' => 'heroicon-o-cube',
            'orders' => 'heroicon-o-shopping-cart',
            'appointments' => 'heroicon-o-calendar',
            'content' => 'heroicon-o-document-text',
            'jobs' => 'heroicon-o-briefcase',
            'engagement' => 'heroicon-o-heart',
            'metal_prices' => 'heroicon-o-currency-dollar',
            'system' => 'heroicon-o-cog-6-tooth',
            'reviews' => 'heroicon-o-star',
            'newsletter' => 'heroicon-o-envelope',
            'stores' => 'heroicon-o-building-storefront',
            'notifications' => 'heroicon-o-bell',
            'categories' => 'heroicon-o-tag',
            'blog' => 'heroicon-o-newspaper',
            'pages' => 'heroicon-o-document',
            'features' => 'heroicon-o-sparkles',
            'testimonials' => 'heroicon-o-chat-bubble-left-ellipsis',
            'sliders' => 'heroicon-o-photo',
            'wishlists' => 'heroicon-o-heart',
            'carts' => 'heroicon-o-shopping-bag',
            default => 'heroicon-o-chart-bar',
        };
    }

    /**
     * تنسيق الأرقام للعرض
     */
    protected function formatNumber(int|float $number): string
    {
        if ($number >= 1000000) {
            return round($number / 1000000, 1) . 'M';
        } elseif ($number >= 1000) {
            return round($number / 1000, 1) . 'K';
        }

        return number_format($number);
    }

    /**
     * تنسيق المبالغ المالية
     */
    protected function formatCurrency(float $amount): string
    {
        return number_format($amount, 2) . ' جنيه';
    }

    /**
     * الحصول على لون الاتجاه (زيادة/نقصان)
     */
    protected function getTrendColor(string $trend): string
    {
        return match($trend) {
            'up' => 'success',
            'down' => 'danger',
            default => 'gray',
        };
    }

    /**
     * الحصول على أيقونة الاتجاه
     */
    protected function getTrendIcon(string $trend): string
    {
        return match($trend) {
            'up' => 'heroicon-o-arrow-trending-up',
            'down' => 'heroicon-o-arrow-trending-down',
            default => 'heroicon-o-minus',
        };
    }

    /**
     * الحصول على وصف الاتجاه
     */
    protected function getTrendDescription(float $percentage, string $trend): string
    {
        $direction = $trend === 'up' ? 'زيادة' : 'نقصان';
        return "{$direction} {$percentage}% عن الشهر الماضي";
    }

    /**
     * الحصول على رابط المورد
     */
    protected function getResourceUrl(string $resource): ?string
    {
        $resourceMap = [
            'users' => 'App\Filament\Resources\UserResource',
            'products' => 'App\Filament\Resources\ProductResource',
            'orders' => 'App\Filament\Resources\OrderResource',
            'appointments' => 'App\Filament\Resources\AppointmentResource',
            'categories' => 'App\Filament\Resources\CategoryResource',
            'blog_posts' => 'App\Filament\Resources\BlogPostResource',
            'pages' => 'App\Filament\Resources\PageResource',
            'jobs' => 'App\Filament\Resources\JobResource',
            'job_applications' => 'App\Filament\Resources\JobApplicationResource',
            'metal_prices' => 'App\Filament\Resources\MetalPriceResource',
            'stores' => 'App\Filament\Resources\StoreResource',
            'newsletters' => 'App\Filament\Resources\NewsletterResource',
            'features' => 'App\Filament\Resources\FeatureResource',
            'testimonials' => 'App\Filament\Resources\TestimonialResource',
            'home_sliders' => 'App\Filament\Resources\HomeSliderResource',
        ];

        $resourceClass = $resourceMap[$resource] ?? null;

        if (!$resourceClass || !class_exists($resourceClass)) {
            return null;
        }

        try {
            return $resourceClass::getUrl('index');
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * مسح كاش الويدجت
     */
    protected function clearCache(): void
    {
        WidgetService::clearWidgetCache();
    }
}
