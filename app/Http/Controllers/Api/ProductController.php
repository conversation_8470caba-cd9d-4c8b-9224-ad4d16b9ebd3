<?php

namespace App\Http\Controllers\Api;

use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductController extends ApiController
{
    /**
     * Display a listing of the products.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = Product::with(['category', 'images']);

        // Filter by category
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by price range
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Search by name
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name_ar', 'LIKE', "%{$search}%")
                  ->orWhere('name_en', 'LIKE', "%{$search}%")
                  ->orWhere('description_ar', 'LIKE', "%{$search}%")
                  ->orWhere('description_en', 'LIKE', "%{$search}%");
            });
        }

        // Sort products
        $sortField = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');

        // Validate sort field
        $allowedSortFields = ['created_at', 'price', 'name_ar', 'name_en'];
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'created_at';
        }

        // Validate sort direction
        $allowedSortDirections = ['asc', 'desc'];
        if (!in_array($sortDirection, $allowedSortDirections)) {
            $sortDirection = 'desc';
        }

        $query->orderBy($sortField, $sortDirection);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $products = $query->paginate($perPage);

        return $this->sendPaginatedResponse(
            $products->items(),
            [
                'total' => $products->total(),
                'per_page' => $products->perPage(),
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
            ],
            'Products retrieved successfully'
        );
    }

    /**
     * Store a newly created product in storage.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description_ar' => 'required|string',
            'description_en' => 'required|string',
            'price' => 'required|numeric|min:0',
            'old_price' => 'nullable|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'sku' => 'required|string|unique:products,sku',
            'stock' => 'required|integer|min:0',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string',
            'show_price' => 'boolean',
            'allow_purchase' => 'boolean',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'additional_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // Handle file upload
        $imagePath = null;
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('products', 'public');
        }

        // Create product
        $product = Product::create([
            'name_ar' => $request->name_ar,
            'name_en' => $request->name_en,
            'slug' => Str::slug($request->name_en),
            'description_ar' => $request->description_ar,
            'description_en' => $request->description_en,
            'price' => $request->price,
            'old_price' => $request->old_price,
            'category_id' => $request->category_id,
            'sku' => $request->sku,
            'stock' => $request->stock,
            'weight' => $request->weight,
            'dimensions' => $request->dimensions,
            'show_price' => $request->show_price ?? true,
            'allow_purchase' => $request->allow_purchase ?? true,
            'is_featured' => $request->is_featured ?? false,
            'is_active' => $request->is_active ?? true,
            'image' => $imagePath,
        ]);

        // Handle additional images
        if ($request->hasFile('additional_images')) {
            foreach ($request->file('additional_images') as $image) {
                $path = $image->store('products', 'public');
                $product->images()->create(['image' => $path]);
            }
        }

        return $this->sendResponse($product, 'Product created successfully', 201);
    }

    /**
     * Display the specified product.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        $product = Product::with(['category', 'images', 'reviews'])->find($id);

        if (is_null($product)) {
            return $this->sendError('Product not found');
        }

        return $this->sendResponse($product, 'Product retrieved successfully');
    }

    /**
     * Update the specified product in storage.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $product = Product::find($id);

        if (is_null($product)) {
            return $this->sendError('Product not found');
        }

        $validator = Validator::make($request->all(), [
            'name_ar' => 'string|max:255',
            'name_en' => 'string|max:255',
            'description_ar' => 'string',
            'description_en' => 'string',
            'price' => 'numeric|min:0',
            'old_price' => 'nullable|numeric|min:0',
            'category_id' => 'exists:categories,id',
            'sku' => 'string|unique:products,sku,' . $id,
            'stock' => 'integer|min:0',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string',
            'show_price' => 'boolean',
            'allow_purchase' => 'boolean',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'additional_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // Handle file upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($product->image) {
                Storage::disk('public')->delete($product->image);
            }
            $imagePath = $request->file('image')->store('products', 'public');
            $product->image = $imagePath;
        }

        // Update product
        $product->fill($request->only([
            'name_ar', 'name_en', 'description_ar', 'description_en',
            'price', 'old_price', 'category_id', 'sku', 'stock',
            'weight', 'dimensions', 'show_price', 'allow_purchase',
            'is_featured', 'is_active'
        ]));

        // Update slug if name_en is changed
        if ($request->has('name_en')) {
            $product->slug = Str::slug($request->name_en);
        }

        $product->save();

        // Handle additional images
        if ($request->hasFile('additional_images')) {
            foreach ($request->file('additional_images') as $image) {
                $path = $image->store('products', 'public');
                $product->images()->create(['image' => $path]);
            }
        }

        return $this->sendResponse($product, 'Product updated successfully');
    }

    /**
     * Remove the specified product from storage.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        $product = Product::find($id);

        if (is_null($product)) {
            return $this->sendError('Product not found');
        }

        // Delete product image
        if ($product->image) {
            Storage::disk('public')->delete($product->image);
        }

        // Delete additional images
        foreach ($product->images as $image) {
            Storage::disk('public')->delete($image->image);
            $image->delete();
        }

        $product->delete();

        return $this->sendResponse(null, 'Product deleted successfully');
    }

    /**
     * الحصول على المنتجات للتطبيق
     */
    public function getForApp(Request $request): JsonResponse
    {
        try {
            $query = Product::where('is_active', true)
                ->with(['category:id,name_ar,name_en,slug']);

            // فلترة حسب الفئة
            if ($request->has('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            // فلترة حسب المنتجات المميزة
            if ($request->has('featured') && $request->featured) {
                $query->where('is_featured', true);
            }

            // فلترة حسب نوع المعدن
            if ($request->has('material_type')) {
                $query->where('material_type', $request->material_type);
            }

            // فلترة حسب العيار
            if ($request->has('metal_purity')) {
                $query->where('metal_purity', $request->metal_purity);
            }

            // البحث في الاسم
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name_ar', 'like', "%{$search}%")
                      ->orWhere('name_en', 'like', "%{$search}%")
                      ->orWhere('description_ar', 'like', "%{$search}%");
                });
            }

            // ترتيب النتائج
            $sortBy = $request->get('sort_by', 'created_at');
            $sortDirection = $request->get('sort_direction', 'desc');

            switch ($sortBy) {
                case 'price':
                    $query->orderBy('price', $sortDirection);
                    break;
                case 'name':
                    $query->orderBy('name_ar', $sortDirection);
                    break;
                case 'weight':
                    $query->orderBy('weight', $sortDirection);
                    break;
                default:
                    $query->orderBy('created_at', $sortDirection);
            }

            // تحديد عدد النتائج
            $perPage = $request->get('per_page', 20);
            $products = $query->paginate($perPage);

            $formattedProducts = $products->getCollection()->map(function ($product) {
                return $this->formatProduct($product);
            });

            return response()->json([
                'success' => true,
                'message' => 'تم جلب المنتجات بنجاح',
                'data' => $formattedProducts,
                'pagination' => [
                    'current_page' => $products->currentPage(),
                    'last_page' => $products->lastPage(),
                    'per_page' => $products->perPage(),
                    'total' => $products->total(),
                    'from' => $products->firstItem(),
                    'to' => $products->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب المنتجات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على منتج محدد للتطبيق
     */
    public function getProduct($id): JsonResponse
    {
        try {
            $product = Product::where('is_active', true)
                ->with(['category:id,name_ar,name_en,slug'])
                ->find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ], 404);
            }

            $formattedProduct = $this->formatProduct($product, true);

            return response()->json([
                'success' => true,
                'message' => 'تم جلب المنتج بنجاح',
                'data' => $formattedProduct
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب المنتج',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على المنتجات المميزة
     */
    public function getFeaturedProducts(): JsonResponse
    {
        try {
            $products = Product::where('is_active', true)
                ->where('is_featured', true)
                ->with(['category:id,name_ar,name_en,slug'])
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            $formattedProducts = $products->map(function ($product) {
                return $this->formatProduct($product);
            });

            return response()->json([
                'success' => true,
                'message' => 'تم جلب المنتجات المميزة بنجاح',
                'data' => $formattedProducts
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب المنتجات المميزة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * تنسيق بيانات المنتج للتطبيق
     */
    private function formatProduct($product, $detailed = false): array
    {
        $formatted = [
            'id' => $product->id,
            'name' => $product->name_ar,
            'name_en' => $product->name_en,
            'description' => $product->description_ar,
            'slug' => $product->slug,
            'price' => (float) $product->price,
            'old_price' => (float) $product->old_price,
            'discount_percentage' => (float) $product->discount_percentage,
            'weight' => (float) $product->weight,
            'metal_purity' => $product->metal_purity,
            'material_type' => $product->material_type,
            'is_featured' => (bool) $product->is_featured,
            'image' => $product->image ? asset('storage/' . $product->image) : null,
            'category' => $product->category ? [
                'id' => $product->category->id,
                'name' => $product->category->name_ar,
                'name_en' => $product->category->name_en,
                'slug' => $product->category->slug,
            ] : null,
        ];

        if ($detailed) {
            $formatted['description_en'] = $product->description_en;
            $formatted['sku'] = $product->sku;
            $formatted['dimensions'] = $product->dimensions;
            $formatted['gallery'] = $product->gallery ? array_map(function ($img) {
                return asset('storage/' . $img);
            }, $product->gallery) : [];
        }

        return $formatted;
    }
}
