<?php

namespace App\Http\Controllers\Api;

use App\Models\MetalPrice;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class MetalPriceController extends ApiController
{
    /**
     * Display a listing of the metal prices.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = MetalPrice::query();

        // Filter by metal type
        if ($request->has('metal_type')) {
            $query->where('metal_type', $request->metal_type);
        }

        // Filter by date range
        if ($request->has('from_date')) {
            $query->whereDate('price_date', '>=', $request->from_date);
        }

        if ($request->has('to_date')) {
            $query->whereDate('price_date', '<=', $request->to_date);
        }

        // Get latest prices
        if ($request->has('latest') && $request->latest) {
            $latestPrices = MetalPrice::selectRaw('metal_type, purity, MAX(date) as max_date')
                ->groupBy('metal_type', 'purity')
                ->get();

            $latestIds = [];
            foreach ($latestPrices as $price) {
                $latestPrice = MetalPrice::where('metal_type', $price->metal_type)
                    ->where('purity', $price->purity)
                    ->whereDate('price_date', $price->max_date)
                    ->first();

                if ($latestPrice) {
                    $latestIds[] = $latestPrice->id;
                }
            }

            $query->whereIn('id', $latestIds);
        }

        // Sort prices
        $sortField = $request->get('sort_by', 'price_date');
        $sortDirection = $request->get('sort_direction', 'desc');

        // Validate sort field
        $allowedSortFields = ['price_date', 'price_per_gram', 'metal_type', 'purity'];
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'price_date';
        }

        // Validate sort direction
        $allowedSortDirections = ['asc', 'desc'];
        if (!in_array($sortDirection, $allowedSortDirections)) {
            $sortDirection = 'desc';
        }

        $query->orderBy($sortField, $sortDirection);

        // Pagination
        if ($request->has('per_page')) {
            $perPage = $request->per_page;
            $prices = $query->paginate($perPage);

            return $this->sendPaginatedResponse(
                $prices->items(),
                [
                    'total' => $prices->total(),
                    'per_page' => $prices->perPage(),
                    'current_page' => $prices->currentPage(),
                    'last_page' => $prices->lastPage(),
                ],
                'Metal prices retrieved successfully'
            );
        }

        // Get all prices
        $prices = $query->get();
        return $this->sendResponse($prices, 'Metal prices retrieved successfully');
    }

    /**
     * Display the specified metal price.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        $price = MetalPrice::find($id);

        if (is_null($price)) {
            return $this->sendError('Metal price not found');
        }

        return $this->sendResponse($price, 'Metal price retrieved successfully');
    }

    /**
     * Get the latest metal prices.
     *
     * @return JsonResponse
     */
    public function latest(): JsonResponse
    {
        $latestPrices = [];

        // Get gold prices
        $goldPurities = [24, 22, 21, 18, 14];
        foreach ($goldPurities as $purity) {
            $price = MetalPrice::where('metal_type', 'gold')
                ->where('purity', $purity)
                ->orderBy('price_date', 'desc')
                ->first();

            if ($price) {
                $latestPrices[] = $price;
            }
        }

        // Get silver price
        $silverPrice = MetalPrice::where('metal_type', 'silver')
            ->orderBy('price_date', 'desc')
            ->first();

        if ($silverPrice) {
            $latestPrices[] = $silverPrice;
        }

        // Get platinum price
        $platinumPrice = MetalPrice::where('metal_type', 'platinum')
            ->orderBy('price_date', 'desc')
            ->first();

        if ($platinumPrice) {
            $latestPrices[] = $platinumPrice;
        }

        return $this->sendResponse($latestPrices, 'Latest metal prices retrieved successfully');
    }

    /**
     * Get historical prices for a specific metal and purity.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function history(Request $request): JsonResponse
    {
        // Validate request
        if (!$request->has('metal_type') || !$request->has('purity')) {
            return $this->sendError('Validation Error', ['error' => 'Metal type and purity are required'], 422);
        }

        $query = MetalPrice::where('metal_type', $request->metal_type)
            ->where('purity', $request->purity);

        // Filter by date range
        if ($request->has('from_date')) {
            $query->whereDate('price_date', '>=', $request->from_date);
        }

        if ($request->has('to_date')) {
            $query->whereDate('price_date', '<=', $request->to_date);
        }

        // Limit results
        if ($request->has('limit')) {
            $query->limit($request->limit);
        }

        // Order by date
        $query->orderBy('price_date', 'desc');

        $prices = $query->get();

        return $this->sendResponse($prices, 'Historical metal prices retrieved successfully');
    }

    /**
     * الحصول على أحدث أسعار المعادن للتطبيق
     */
    public function getCurrentPrices(): JsonResponse
    {
        try {
            // جلب أحدث الأسعار النشطة لكل معدن وعيار
            $prices = MetalPrice::where('is_active', true)
                ->where('currency', 'EGP')
                ->orderBy('metal_type')
                ->orderBy('purity')
                ->get()
                ->groupBy('metal_type');

            $formattedPrices = [];

            foreach ($prices as $metalType => $metalPrices) {
                $metalData = [
                    'metal_type' => $metalType,
                    'metal_name' => $this->getMetalName($metalType),
                    'prices' => []
                ];

                foreach ($metalPrices as $price) {
                    $metalData['prices'][] = [
                        'id' => $price->id,
                        'purity' => $price->purity,
                        'purity_name' => $this->getPurityName($price->purity),
                        'price_per_gram' => (float) $price->price_per_gram,
                        'price_per_ounce' => (float) $price->price_per_ounce,
                        'price_per_piece' => (float) $price->price_per_piece,
                        'currency' => $price->currency,
                        'last_updated' => $price->updated_at->format('Y-m-d H:i:s'),
                        'last_updated_human' => $price->updated_at->diffForHumans(),
                    ];
                }

                $formattedPrices[] = $metalData;
            }

            return $this->sendResponse($formattedPrices, 'تم جلب أسعار المعادن بنجاح');

        } catch (\Exception $e) {
            return $this->sendError('حدث خطأ في جلب أسعار المعادن', ['error' => $e->getMessage()]);
        }
    }

    /**
     * الحصول على أسعار معدن محدد
     */
    public function getMetalPrices(string $metalType): JsonResponse
    {
        try {
            $prices = MetalPrice::where('metal_type', $metalType)
                ->where('is_active', true)
                ->where('currency', 'EGP')
                ->orderBy('purity')
                ->get();

            if ($prices->isEmpty()) {
                return $this->sendError('لا توجد أسعار متاحة لهذا المعدن');
            }

            $formattedPrices = $prices->map(function ($price) {
                return [
                    'id' => $price->id,
                    'purity' => $price->purity,
                    'purity_name' => $this->getPurityName($price->purity),
                    'price_per_gram' => (float) $price->price_per_gram,
                    'price_per_ounce' => (float) $price->price_per_ounce,
                    'price_per_piece' => (float) $price->price_per_piece,
                    'currency' => $price->currency,
                    'last_updated' => $price->updated_at->format('Y-m-d H:i:s'),
                    'last_updated_human' => $price->updated_at->diffForHumans(),
                ];
            });

            $data = [
                'metal_type' => $metalType,
                'metal_name' => $this->getMetalName($metalType),
                'prices' => $formattedPrices
            ];

            return $this->sendResponse($data, 'تم جلب أسعار المعدن بنجاح');

        } catch (\Exception $e) {
            return $this->sendError('حدث خطأ في جلب أسعار المعدن', ['error' => $e->getMessage()]);
        }
    }

    /**
     * الحصول على تاريخ أسعار معدن محدد
     */
    public function getPriceHistory(string $metalType, string $purity, Request $request): JsonResponse
    {
        try {
            $days = $request->get('days', 30); // افتراضي 30 يوم
            $startDate = Carbon::now()->subDays($days);

            $priceHistory = MetalPrice::where('metal_type', $metalType)
                ->where('purity', $purity)
                ->where('currency', 'EGP')
                ->where('price_date', '>=', $startDate)
                ->orderBy('price_date')
                ->get();

            if ($priceHistory->isEmpty()) {
                return $this->sendError('لا يوجد تاريخ أسعار متاح لهذا المعدن والعيار');
            }

            $formattedHistory = $priceHistory->map(function ($price) {
                return [
                    'date' => $price->price_date,
                    'price_per_gram' => (float) $price->price_per_gram,
                    'price_per_ounce' => (float) $price->price_per_ounce,
                    'price_per_piece' => (float) $price->price_per_piece,
                ];
            });

            // حساب الإحصائيات
            $prices = $formattedHistory->pluck('price_per_gram');
            $stats = [
                'highest' => $prices->max(),
                'lowest' => $prices->min(),
                'average' => round($prices->average(), 2),
                'current' => $prices->last(),
                'change' => $prices->count() > 1 ? round($prices->last() - $prices->first(), 2) : 0,
                'change_percentage' => $prices->count() > 1 && $prices->first() > 0
                    ? round((($prices->last() - $prices->first()) / $prices->first()) * 100, 2)
                    : 0,
            ];

            $data = [
                'metal_type' => $metalType,
                'metal_name' => $this->getMetalName($metalType),
                'purity' => $purity,
                'purity_name' => $this->getPurityName($purity),
                'period_days' => $days,
                'statistics' => $stats,
                'history' => $formattedHistory
            ];

            return $this->sendResponse($data, 'تم جلب تاريخ الأسعار بنجاح');

        } catch (\Exception $e) {
            return $this->sendError('حدث خطأ في جلب تاريخ الأسعار', ['error' => $e->getMessage()]);
        }
    }

    /**
     * حساب قيمة المجوهرات
     */
    public function calculateJewelryValue(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'metal_type' => 'required|string|in:gold,silver,platinum',
                'purity' => 'required|string',
                'weight' => 'required|numeric|min:0.001',
                'unit' => 'string|in:gram,ounce|nullable'
            ]);

            $metalType = $request->metal_type;
            $purity = $request->purity;
            $weight = (float) $request->weight;
            $unit = $request->get('unit', 'gram');

            // جلب السعر الحالي
            $currentPrice = MetalPrice::where('metal_type', $metalType)
                ->where('purity', $purity)
                ->where('is_active', true)
                ->where('currency', 'EGP')
                ->first();

            if (!$currentPrice) {
                return $this->sendError('لا يوجد سعر متاح لهذا المعدن والعيار');
            }

            // حساب القيمة
            $pricePerUnit = $unit === 'ounce' ? $currentPrice->price_per_ounce : $currentPrice->price_per_gram;
            $totalValue = $weight * $pricePerUnit;

            $data = [
                'metal_type' => $metalType,
                'metal_name' => $this->getMetalName($metalType),
                'purity' => $purity,
                'purity_name' => $this->getPurityName($purity),
                'weight' => $weight,
                'unit' => $unit,
                'price_per_unit' => (float) $pricePerUnit,
                'total_value' => round($totalValue, 2),
                'currency' => 'EGP',
                'calculation_date' => now()->format('Y-m-d H:i:s'),
            ];

            return $this->sendResponse($data, 'تم حساب قيمة المجوهرات بنجاح');

        } catch (\Exception $e) {
            return $this->sendError('حدث خطأ في حساب قيمة المجوهرات', ['error' => $e->getMessage()]);
        }
    }

    /**
     * الحصول على اسم المعدن بالعربية
     */
    private function getMetalName(string $metalType): string
    {
        $names = [
            'gold' => 'الذهب',
            'silver' => 'الفضة',
            'platinum' => 'البلاتين',
            'gold_coin' => 'الجنيهات الذهبية'
        ];

        return $names[$metalType] ?? $metalType;
    }

    /**
     * الحصول على اسم العيار بالعربية
     */
    private function getPurityName(string $purity): string
    {
        $names = [
            // عيارات الذهب
            '24K' => '24 قيراط',
            '22K' => '22 قيراط',
            '21K' => '21 قيراط',
            '18K' => '18 قيراط',
            '14K' => '14 قيراط',
            '12K' => '12 قيراط',
            '9K' => '9 قيراط',

            // عيارات الفضة
            '999' => 'فضة 999',
            '925' => 'فضة 925',
            '900' => 'فضة 900',
            '800' => 'فضة 800',
            '600' => 'فضة 600',

            // الجنيهات الذهبية
            'جنيه_ذهب' => 'جنيه ذهب',
            'نصف_جنيه_ذهب' => 'نصف جنيه ذهب',
            'ربع_جنيه_ذهب' => 'ربع جنيه ذهب',
        ];

        return $names[$purity] ?? $purity;
    }
}
