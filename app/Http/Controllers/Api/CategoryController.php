<?php

namespace App\Http\Controllers\Api;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CategoryController extends ApiController
{
    /**
     * Display a listing of the categories.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = Category::query();

        // Filter by parent category
        if ($request->has('parent_id')) {
            $query->where('parent_id', $request->parent_id);
        }

        // Get only parent categories
        if ($request->has('only_parents') && $request->only_parents) {
            $query->whereNull('parent_id');
        }

        // Include products count
        if ($request->has('with_products_count') && $request->with_products_count) {
            $query->withCount('products');
        }

        // Include children
        if ($request->has('with_children') && $request->with_children) {
            $query->with('children');
        }

        // Sort categories
        $sortField = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');

        // Validate sort field
        $allowedSortFields = ['created_at', 'name', 'id'];
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'created_at';
        }

        // Validate sort direction
        $allowedSortDirections = ['asc', 'desc'];
        if (!in_array($sortDirection, $allowedSortDirections)) {
            $sortDirection = 'desc';
        }

        $query->orderBy($sortField, $sortDirection);

        // Pagination
        if ($request->has('per_page')) {
            $perPage = $request->per_page;
            $categories = $query->paginate($perPage);

            return $this->sendPaginatedResponse(
                $categories->items(),
                [
                    'total' => $categories->total(),
                    'per_page' => $categories->perPage(),
                    'current_page' => $categories->currentPage(),
                    'last_page' => $categories->lastPage(),
                ],
                'Categories retrieved successfully'
            );
        }

        // Get all categories
        $categories = $query->get();
        return $this->sendResponse($categories, 'Categories retrieved successfully');
    }

    /**
     * Store a newly created category in storage.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // Handle file upload
        $imagePath = null;
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('categories', 'public');
        }

        // Create category
        $category = Category::create([
            'name' => $request->name,
            'name_ar' => $request->name_ar,
            'name_en' => $request->name_en,
            'slug' => Str::slug($request->name_en),
            'description' => $request->description,
            'description_ar' => $request->description_ar,
            'description_en' => $request->description_en,
            'parent_id' => $request->parent_id,
            'image' => $imagePath,
            'is_active' => $request->is_active ?? true,
        ]);

        return $this->sendResponse($category, 'Category created successfully', 201);
    }

    /**
     * Display the specified category.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        $category = Category::with(['parent', 'children'])->find($id);

        if (is_null($category)) {
            return $this->sendError('Category not found');
        }

        // Include products count
        $category->loadCount('products');

        return $this->sendResponse($category, 'Category retrieved successfully');
    }

    /**
     * Update the specified category in storage.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $category = Category::find($id);

        if (is_null($category)) {
            return $this->sendError('Category not found');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'name_ar' => 'string|max:255',
            'name_en' => 'string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // Prevent setting parent to self
        if ($request->has('parent_id') && $request->parent_id == $id) {
            return $this->sendError('Validation Error.', ['parent_id' => ['A category cannot be its own parent']], 422);
        }

        // Handle file upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($category->image) {
                Storage::disk('public')->delete($category->image);
            }
            $imagePath = $request->file('image')->store('categories', 'public');
            $category->image = $imagePath;
        }

        // Update category
        $category->fill($request->only([
            'name', 'name_ar', 'name_en', 'description', 'description_ar',
            'description_en', 'parent_id', 'is_active'
        ]));

        // Update slug if name_en is changed
        if ($request->has('name_en')) {
            $category->slug = Str::slug($request->name_en);
        }

        $category->save();

        return $this->sendResponse($category, 'Category updated successfully');
    }

    /**
     * Remove the specified category from storage.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        $category = Category::find($id);

        if (is_null($category)) {
            return $this->sendError('Category not found');
        }

        // Check if category has children
        if ($category->children()->count() > 0) {
            return $this->sendError('Cannot delete category with children. Delete children first or reassign them.');
        }

        // Check if category has products
        if ($category->products()->count() > 0) {
            return $this->sendError('Cannot delete category with products. Delete products first or reassign them.');
        }

        // Delete category image
        if ($category->image) {
            Storage::disk('public')->delete($category->image);
        }

        $category->delete();

        return $this->sendResponse(null, 'Category deleted successfully');
    }

    /**
     * الحصول على الفئات للتطبيق (مبسطة)
     */
    public function getForApp(): JsonResponse
    {
        try {
            $categories = Category::where('is_active', true)
                ->whereNull('parent_id') // الفئات الرئيسية فقط
                ->with(['children' => function ($query) {
                    $query->where('is_active', true)
                          ->withCount('products');
                }])
                ->withCount('products')
                ->orderBy('order')
                ->get();

            $formattedCategories = $categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name_ar,
                    'name_en' => $category->name_en,
                    'description' => $category->description_ar,
                    'slug' => $category->slug,
                    'image' => $category->image ? asset('storage/' . $category->image) : null,
                    'products_count' => $category->products_count,
                    'children' => $category->children->map(function ($child) {
                        return [
                            'id' => $child->id,
                            'name' => $child->name_ar,
                            'name_en' => $child->name_en,
                            'description' => $child->description_ar,
                            'slug' => $child->slug,
                            'image' => $child->image ? asset('storage/' . $child->image) : null,
                            'products_count' => $child->products_count,
                        ];
                    })
                ];
            });

            return $this->sendResponse($formattedCategories, 'تم جلب الفئات بنجاح');

        } catch (\Exception $e) {
            return $this->sendError('حدث خطأ في جلب الفئات', ['error' => $e->getMessage()]);
        }
    }

    /**
     * الحصول على فئة محددة مع منتجاتها للتطبيق
     */
    public function getCategoryWithProducts($id): JsonResponse
    {
        try {
            $category = Category::where('is_active', true)
                ->with(['products' => function ($query) {
                    $query->where('is_active', true)
                          ->select('id', 'name_ar', 'name_en', 'description_ar', 'slug',
                                  'category_id', 'weight', 'metal_purity', 'material_type',
                                  'price', 'old_price', 'discount_percentage', 'is_featured',
                                  'image', 'gallery');
                }])
                ->find($id);

            if (!$category) {
                return $this->sendError('الفئة غير موجودة');
            }

            $formattedCategory = [
                'id' => $category->id,
                'name' => $category->name_ar,
                'name_en' => $category->name_en,
                'description' => $category->description_ar,
                'slug' => $category->slug,
                'image' => $category->image ? asset('storage/' . $category->image) : null,
                'products_count' => $category->products->count(),
                'products' => $category->products->map(function ($product) {
                    return [
                        'id' => $product->id,
                        'name' => $product->name_ar,
                        'name_en' => $product->name_en,
                        'description' => $product->description_ar,
                        'slug' => $product->slug,
                        'weight' => (float) $product->weight,
                        'metal_purity' => $product->metal_purity,
                        'material_type' => $product->material_type,
                        'price' => (float) $product->price,
                        'old_price' => (float) $product->old_price,
                        'discount_percentage' => (float) $product->discount_percentage,
                        'is_featured' => (bool) $product->is_featured,
                        'image' => $product->image ? asset('storage/' . $product->image) : null,
                        'gallery' => $product->gallery ? array_map(function ($img) {
                            return asset('storage/' . $img);
                        }, $product->gallery) : [],
                    ];
                })
            ];

            return $this->sendResponse($formattedCategory, 'تم جلب الفئة والمنتجات بنجاح');

        } catch (\Exception $e) {
            return $this->sendError('حدث خطأ في جلب الفئة', ['error' => $e->getMessage()]);
        }
    }
}
