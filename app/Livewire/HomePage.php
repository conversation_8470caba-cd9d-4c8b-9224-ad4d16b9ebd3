<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Product;
use App\Models\Category;
use App\Models\MetalPrice;

use App\Models\HomeSlider;
use App\Models\Feature;
use App\Models\Testimonial;
use App\Traits\CategorySettingsHelper;
use App\Traits\SettingsHelper;

class HomePage extends Component
{
    use CategorySettingsHelper;
    use SettingsHelper;
    public $featuredProducts = [];
    public $newArrivals = [];
    public $categories = [];
    public $goldPrices = [];
    public $sliders = [];
    public $features = [];
    public $testimonials = [];

    public function mount()
    {
        // Get settings
        $settings = app(\App\Services\SettingsService::class)->all();

        // Get home sliders
        $this->sliders = HomeSlider::where('is_active', true)
            ->orderBy('order')
            ->get();

        // Features will be loaded in render() based on settings

        // Get featured products if enabled in settings
        if ($settings->show_featured_products ?? true) {
            $this->featuredProducts = Product::where('is_featured', true)
                ->where('is_active', true)
                ->with('category')
                ->withCount('reviews')
                ->take(8)
                ->get();

            // Add default rating for products and category settings
            foreach ($this->featuredProducts as $product) {
                $product->rating = $product->reviews_count > 0 ? rand(4, 5) : rand(3, 5);
                $categorySettings = $this->getCategorySettings($product);
                $product->show_price = $categorySettings['show_price'];
            }
        }

        // Get new arrivals if enabled in settings
        if ($settings->show_new_arrivals ?? true) {
            $this->newArrivals = Product::where('is_active', true)
                ->orderBy('created_at', 'desc')
                ->with('category')
                ->withCount('reviews')
                ->take(8)
                ->get();

            // Add default rating for products and category settings
            foreach ($this->newArrivals as $product) {
                $product->rating = $product->reviews_count > 0 ? rand(4, 5) : rand(3, 5);
                $categorySettings = $this->getCategorySettings($product);
                $product->show_price = $categorySettings['show_price'];
            }
        }

        // Get main categories if enabled in settings
        if ($settings->show_categories ?? true) {
            $this->categories = Category::where('parent_id', null)
                ->where('is_active', true)
                ->withCount('products')
                ->take(6)
                ->get();
                // dd($this->categories);
        }

        // Get latest gold prices if enabled in settings
        if ($settings->show_gold_prices ?? true) {
            // جلب أحدث الأسعار النشطة لكل عيار من الذهب
            $goldPricesRaw = MetalPrice::where('metal_type', 'gold')
                ->where('currency', 'EGP')
                ->where('is_active', true)
                ->get()
                ->keyBy('purity');

            // ترتيب الأسعار منطقياً (من الأعلى للأقل)
            $goldOrder = ['24K', '22K', '21K', '18K', '14K', '12K', '9K'];
            $this->goldPrices = collect();
            foreach ($goldOrder as $purity) {
                if ($goldPricesRaw->has($purity)) {
                    $this->goldPrices->push($goldPricesRaw->get($purity));
                }
            }

            // إذا لم توجد أسعار نشطة، جلب آخر الأسعار المتاحة
            if ($this->goldPrices->isEmpty()) {
                // تحديد آخر تاريخ متاح للأسعار
                $latestDate = MetalPrice::where('metal_type', 'gold')
                    ->where('currency', 'EGP')
                    ->max('price_date');

                if ($latestDate) {
                    $latestPricesRaw = MetalPrice::where('metal_type', 'gold')
                        ->where('currency', 'EGP')
                        ->whereDate('price_date', $latestDate)
                        ->get()
                        ->keyBy('purity');

                    // ترتيب الأسعار منطقياً (من الأعلى للأقل)
                    $this->goldPrices = collect();
                    foreach ($goldOrder as $purity) {
                        if ($latestPricesRaw->has($purity)) {
                            $this->goldPrices->push($latestPricesRaw->get($purity));
                        }
                    }
                }
            }
        }
    }

    public function render()
    {
        // Get site settings for features
        $siteSettings = $this->getSiteSettings();
        $showFeatures = $siteSettings ? $siteSettings->show_features : true;
        $showTestimonials = $siteSettings ? $siteSettings->show_testimonials : true;
        $showNewsletter = $siteSettings ? $siteSettings->show_newsletter : true;

        // Get current locale
        $locale = app()->getLocale();

        // Get active features based on settings
        if ($showFeatures) {
            $this->features = Feature::where('is_active', true)
                ->orderBy('order')
                ->get();
        } else {
            $this->features = collect([]);
        }

        // Get active testimonials based on settings
        if ($showTestimonials) {
            $this->testimonials = Testimonial::where('is_active', true)
                ->orderBy('order')
                ->take(3)
                ->get();
        } else {
            $this->testimonials = collect([]);
        }

        return view('livewire.home-page', array_merge([
            'locale' => $locale,
            'showFeatures' => $showFeatures,
            'showTestimonials' => $showTestimonials,
            'showNewsletter' => $showNewsletter
        ], $this->getViewSettings()));
    }
}
