# نظام فلترة الصلاحيات في Filament

## نظرة عامة

تم تطبيق نظام شامل لفلترة الصلاحيات في Filament يضمن أن كل مستخدم يرى فقط العناصر التي يملك صلاحية الوصول إليها. هذا النظام يطبق نفس المنطق المستخدم في `RoleResource` على جميع عناصر Filament.

## المكونات الرئيسية

### 1. خدمة إدارة الصلاحيات المركزية
**الملف**: `app/Services/FilamentPermissionService.php`

هذه الخدمة تحتوي على جميع منطق فلترة الصلاحيات:

- `getAvailablePermissions()`: جلب الصلاحيات المتاحة للمستخدم الحالي
- `canAccessResource()`: التحقق من صلاحية الوصول للموارد
- `canAccessPage()`: التحقق من صلاحية الوصول للصفحات
- `canAccessWidget()`: التحقق من صلاحية الوصول للودجات
- `filterResources()`: فلترة قائمة الموارد
- `filterPages()`: فلترة قائمة الصفحات
- `filterWidgets()`: فلترة قائمة الودجات

### 2. Traits للصلاحيات

#### HasPermissionFiltering (للموارد)
**الملف**: `app/Traits/HasPermissionFiltering.php`

يوفر الدوال التالية للموارد:
- `shouldRegisterNavigation()`: تحديد إظهار المورد في التنقل
- `canAccess()`: تحديد إمكانية الوصول للمورد
- `canViewAny()`: تحديد إمكانية عرض السجلات
- `canCreate()`: تحديد إمكانية الإنشاء
- `canEdit()`: تحديد إمكانية التعديل
- `canDelete()`: تحديد إمكانية الحذف

#### HasPagePermissionFiltering (للصفحات)
**الملف**: `app/Traits/HasPagePermissionFiltering.php`

يوفر الدوال التالية للصفحات:
- `shouldRegisterNavigation()`: تحديد إظهار الصفحة في التنقل
- `canAccess()`: تحديد إمكانية الوصول للصفحة

#### HasWidgetPermissionFiltering (للودجات)
**الملف**: `app/Traits/HasWidgetPermissionFiltering.php`

يوفر الدوال التالية للودجات:
- `canView()`: تحديد إمكانية عرض الودجة

### 3. تحديث AdminPanelProvider
**الملف**: `app/Providers/Filament/AdminPanelProvider.php`

تم تحديث مزود الخدمة ليستخدم الفلترة:
```php
->resources(FilamentPermissionService::getAvailableResourceClasses())
->pages(array_merge(
    [\App\Filament\Pages\DashboardPage::class],
    FilamentPermissionService::getAvailablePageClasses()
))
->widgets(array_merge(
    [Widgets\AccountWidget::class],
    FilamentPermissionService::getAvailableWidgetClasses()
))
```

## الموارد المطبق عليها النظام

### الموارد (Resources)
تم تطبيق `HasPermissionFiltering` على:
- ✅ UserResource
- ✅ RoleResource  
- ✅ PermissionResource
- ✅ ProductResource
- ✅ CategoryResource
- ✅ OrderResource
- ✅ AppointmentResource
- ✅ BlogPostResource
- ✅ PageResource
- ✅ FeatureResource
- ✅ TestimonialResource
- ✅ HomeSliderResource
- ✅ StoreResource
- ✅ JobResource
- ✅ JobApplicationResource
- ✅ NewsletterResource
- ✅ MetalTypeResource
- ✅ MetalPurityResource
- ✅ MetalPriceResource
- ✅ SiteSettingResource
- ✅ SuperAdminSettingResource
- ✅ SettingChangeResource
- ✅ LanguageResource
- ✅ LanguageManagerResource

### الصفحات (Pages)
تم تطبيق `HasPagePermissionFiltering` على:
- ✅ DashboardPage
- ✅ PreviewSettings
- ✅ SearchSettings
- ✅ SiteSettingsManager
- ✅ TranslationsManager

### الودجات (Widgets)
تم تطبيق `HasWidgetPermissionFiltering` على:
- ✅ StatsOverview
- ✅ SalesChart
- ✅ GoldPriceChart
- ✅ LatestOrders
- ✅ UpcomingAppointments
- ✅ MaintenanceModeToggle

## منطق الصلاحيات

### للمستخدم Super Admin
- يرى **جميع** الموارد والصفحات والودجات
- لا توجد قيود على الوصول

### للمستخدم Admin
- يرى فقط الموارد التي يملك صلاحية `view_any_` لها
- يرى فقط الصفحات التي يملك صلاحية `page_` لها  
- يرى فقط الودجات التي يملك صلاحية `widget_` لها

### للمستخدمين الآخرين
- نفس منطق Admin لكن مع صلاحيات أقل

## أمثلة على الفلترة

### مثال: مستخدم Admin
```
الموارد المتاحة: 20/24 (83%)
- ✅ UserResource (لديه view_any_user)
- ✅ ProductResource (لديه view_any_product)
- ❌ SuperAdminSettingResource (ليس لديه view_any_super::admin::setting)

الودجات المتاحة: 3/6 (50%)
- ✅ StatsOverview (لديه widget_StatsOverview)
- ❌ MaintenanceModeToggle (ليس لديه widget_MaintenanceModeToggle)
```

## التحقق من عمل النظام

### اختبار سريع
```bash
php test_filament_permissions.php
```

### النتائج المتوقعة
- **Super Admin**: يرى جميع العناصر
- **Admin**: يرى معظم العناصر عدا إعدادات Super Admin
- **Manager**: يرى عناصر محدودة حسب الصلاحيات

## الفوائد

### 1. الأمان
- كل مستخدم يرى فقط ما يحتاجه
- منع الوصول غير المصرح به للموارد الحساسة

### 2. تجربة المستخدم
- واجهة نظيفة ومنظمة
- عدم إرباك المستخدمين بعناصر لا يمكنهم استخدامها

### 3. سهولة الإدارة
- نظام مركزي لإدارة الصلاحيات
- سهولة إضافة موارد جديدة

### 4. الأداء
- تحميل أقل للعناصر غير المطلوبة
- استعلامات قاعدة بيانات محسنة

## إضافة موارد جديدة

عند إضافة مورد جديد:

1. أضف `use App\Traits\HasPermissionFiltering;` في بداية الملف
2. أضف `use HasPermissionFiltering;` داخل الكلاس
3. أضف المورد لقائمة `getAvailableResourceClasses()` في `FilamentPermissionService`

مثال:
```php
<?php

namespace App\Filament\Resources;

use App\Traits\HasPermissionFiltering;
use Filament\Resources\Resource;

class NewResource extends Resource
{
    use HasPermissionFiltering;
    
    // باقي الكود...
}
```

## الصيانة والتطوير

### إضافة صلاحيات جديدة
1. أنشئ الصلاحيات في قاعدة البيانات
2. أضفها للأدوار المناسبة
3. النظام سيطبق الفلترة تلقائياً

### تخصيص منطق الفلترة
يمكن تخصيص منطق الفلترة في `FilamentPermissionService` حسب الحاجة.

## الخلاصة

تم تطبيق نظام شامل لفلترة الصلاحيات في Filament يضمن:
- ✅ أمان عالي
- ✅ تجربة مستخدم محسنة  
- ✅ سهولة الإدارة
- ✅ أداء محسن
- ✅ قابلية التوسع

النظام يعمل بنجاح ويطبق نفس المنطق المستخدم في `RoleResource` على جميع عناصر Filament.
