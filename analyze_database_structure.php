<?php

require_once 'vendor/autoload.php';

// تحميل Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 تحليل هيكل قاعدة البيانات والبيانات الموجودة\n";
echo "=============================================\n\n";

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// الحصول على جميع الجداول
$tables = DB::select('SHOW TABLES');
$databaseName = DB::getDatabaseName();
$tableKey = "Tables_in_{$databaseName}";

echo "📊 الجداول الموجودة في قاعدة البيانات:\n";
echo "=====================================\n";

$tableData = [];

foreach ($tables as $table) {
    $tableName = $table->$tableKey;
    
    // تجاهل جداول Laravel الداخلية
    if (in_array($tableName, ['migrations', 'failed_jobs', 'password_reset_tokens', 'personal_access_tokens'])) {
        continue;
    }
    
    // الحصول على عدد الصفوف
    $count = DB::table($tableName)->count();
    
    // الحصول على أعمدة الجدول
    $columns = Schema::getColumnListing($tableName);
    
    $tableData[$tableName] = [
        'count' => $count,
        'columns' => $columns
    ];
    
    echo "📋 {$tableName}: {$count} صف\n";
    echo "   الأعمدة: " . implode(', ', $columns) . "\n\n";
}

// تحليل العلاقات والترتيب المطلوب
echo "🔗 تحليل العلاقات وترتيب الـ Seeders:\n";
echo "===================================\n";

$seederOrder = [
    // الجداول الأساسية (بدون foreign keys)
    'Basic Tables' => [
        'languages' => 'اللغات',
        'roles' => 'الأدوار', 
        'permissions' => 'الصلاحيات',
        'site_settings' => 'إعدادات الموقع',
        'super_admin_settings' => 'إعدادات السوبر أدمن',
    ],
    
    // جداول المستخدمين والأدوار
    'User Management' => [
        'users' => 'المستخدمين',
        'model_has_permissions' => 'صلاحيات النماذج',
        'model_has_roles' => 'أدوار النماذج', 
        'role_has_permissions' => 'صلاحيات الأدوار',
    ],
    
    // جداول المحتوى الأساسي
    'Content Tables' => [
        'categories' => 'الفئات',
        'features' => 'المميزات',
        'testimonials' => 'الشهادات',
        'home_sliders' => 'شرائح الصفحة الرئيسية',
        'pages' => 'الصفحات',
        'blog_posts' => 'مقالات المدونة',
    ],
    
    // جداول المنتجات والأسعار
    'Products & Pricing' => [
        'metal_types' => 'أنواع المعادن',
        'metal_purities' => 'عيارات المعادن',
        'metal_prices' => 'أسعار المعادن',
        'products' => 'المنتجات',
    ],
    
    // جداول الأعمال والخدمات
    'Business Tables' => [
        'stores' => 'المتاجر',
        'jobs' => 'الوظائف',
        'job_applications' => 'طلبات التوظيف',
        'appointments' => 'المواعيد',
    ],
    
    // جداول التفاعل
    'Interaction Tables' => [
        'orders' => 'الطلبات',
        'order_items' => 'عناصر الطلبات',
        'reviews' => 'التقييمات',
        'newsletters' => 'النشرة الإخبارية',
        'addresses' => 'العناوين',
    ],
];

foreach ($seederOrder as $category => $tables) {
    echo "📂 {$category}:\n";
    foreach ($tables as $table => $description) {
        $count = isset($tableData[$table]) ? $tableData[$table]['count'] : 0;
        $status = $count > 0 ? "✅ {$count} صف" : "⚪ فارغ";
        echo "   - {$table} ({$description}): {$status}\n";
    }
    echo "\n";
}

// فحص البيانات الهامة
echo "🎯 البيانات الهامة للـ Seeding:\n";
echo "=============================\n";

// فحص المستخدمين والأدوار
if (isset($tableData['users']) && $tableData['users']['count'] > 0) {
    $users = DB::table('users')->select('id', 'name', 'email', 'created_at')->get();
    echo "👥 المستخدمين ({$users->count()}):\n";
    foreach ($users as $user) {
        echo "   - {$user->name} ({$user->email})\n";
    }
    echo "\n";
}

// فحص الأدوار
if (isset($tableData['roles']) && $tableData['roles']['count'] > 0) {
    $roles = DB::table('roles')->select('name', 'guard_name')->get();
    echo "🎭 الأدوار ({$roles->count()}):\n";
    foreach ($roles as $role) {
        echo "   - {$role->name} ({$role->guard_name})\n";
    }
    echo "\n";
}

// فحص الفئات
if (isset($tableData['categories']) && $tableData['categories']['count'] > 0) {
    $categories = DB::table('categories')->select('name_ar', 'name_en', 'parent_id')->get();
    echo "📂 الفئات ({$categories->count()}):\n";
    foreach ($categories as $category) {
        $parent = $category->parent_id ? " (فرعية)" : " (رئيسية)";
        echo "   - {$category->name_ar} / {$category->name_en}{$parent}\n";
    }
    echo "\n";
}

// فحص المنتجات
if (isset($tableData['products']) && $tableData['products']['count'] > 0) {
    $products = DB::table('products')->select('name_ar', 'name_en', 'category_id', 'is_featured')->get();
    echo "🛍️ المنتجات ({$products->count()}):\n";
    $featured = $products->where('is_featured', 1)->count();
    echo "   - منتجات مميزة: {$featured}\n";
    echo "   - منتجات عادية: " . ($products->count() - $featured) . "\n";
    echo "\n";
}

// فحص أسعار المعادن
if (isset($tableData['metal_prices']) && $tableData['metal_prices']['count'] > 0) {
    $metalPrices = DB::table('metal_prices')
        ->select('metal_type', 'purity', 'currency', 'is_active')
        ->get();
    echo "💰 أسعار المعادن ({$metalPrices->count()}):\n";
    $byMetal = $metalPrices->groupBy('metal_type');
    foreach ($byMetal as $metal => $prices) {
        $active = $prices->where('is_active', 1)->count();
        echo "   - {$metal}: {$prices->count()} سعر ({$active} نشط)\n";
    }
    echo "\n";
}

// إحصائيات عامة
echo "📊 إحصائيات عامة:\n";
echo "==================\n";
$totalTables = count($tableData);
$tablesWithData = count(array_filter($tableData, fn($table) => $table['count'] > 0));
$emptyTables = $totalTables - $tablesWithData;
$totalRecords = array_sum(array_column($tableData, 'count'));

echo "📋 إجمالي الجداول: {$totalTables}\n";
echo "✅ جداول تحتوي على بيانات: {$tablesWithData}\n";
echo "⚪ جداول فارغة: {$emptyTables}\n";
echo "📊 إجمالي الصفوف: {$totalRecords}\n";

echo "\n🎯 خطة إنشاء الـ Seeders:\n";
echo "========================\n";
echo "1. إنشاء {$tablesWithData} seeder للجداول التي تحتوي على بيانات\n";
echo "2. ترتيب الـ seeders حسب العلاقات\n";
echo "3. إضافة فحص التكرار لكل seeder\n";
echo "4. تحديث DatabaseSeeder.php\n";
echo "5. اختبار النظام الكامل\n";

echo "\n";
