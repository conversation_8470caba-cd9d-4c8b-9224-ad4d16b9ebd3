<?php

require_once 'vendor/autoload.php';

// تحميل Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 تشخيص مشكلة دور Admin\n";
echo "========================\n\n";

use App\Services\SuperAdminProtectionService;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Auth;

// البحث عن مستخدم Admin
$adminUser = User::role('admin')->first();

if (!$adminUser) {
    echo "❌ لا يوجد مستخدم Admin للاختبار!\n";
    
    // البحث عن أي مستخدم غير Super Admin
    $adminUser = User::whereDoesntHave('roles', function($q) {
        $q->where('name', 'super_admin');
    })->first();
    
    if ($adminUser) {
        echo "🔄 سيتم الاختبار مع مستخدم: {$adminUser->name}\n";
        echo "الأدوار: " . $adminUser->roles->pluck('name')->implode(', ') . "\n\n";
    } else {
        echo "❌ لا يوجد أي مستخدم غير Super Admin للاختبار!\n";
        exit(1);
    }
} else {
    echo "👤 اختبار مع Admin: {$adminUser->name}\n";
    echo "الأدوار: " . $adminUser->roles->pluck('name')->implode(', ') . "\n\n";
}

// تسجيل دخول Admin
Auth::login($adminUser);

// اختبار 1: التحقق من isSuperAdmin()
echo "🧪 اختبار 1: دالة isSuperAdmin()\n";
$isSuperAdmin = SuperAdminProtectionService::isSuperAdmin();
echo "النتيجة: " . ($isSuperAdmin ? '⚠️ يتم التعرف عليه كـ Super Admin!' : '✅ ليس Super Admin') . "\n\n";

// اختبار 2: فحص الصلاحيات
echo "🧪 اختبار 2: صلاحيات Filament\n";
$user = Auth::user();
$filamentPermissions = [
    'view_any_user' => 'عرض جميع المستخدمين',
    'create_user' => 'إنشاء مستخدم',
    'update_user' => 'تعديل مستخدم',
    'delete_user' => 'حذف مستخدم',
    'view_any_role' => 'عرض جميع الأدوار',
    'create_role' => 'إنشاء دور',
    'update_role' => 'تعديل دور',
    'delete_role' => 'حذف دور',
];

$hasAnyPermission = false;
foreach ($filamentPermissions as $permission => $description) {
    $hasPermission = $user->can($permission);
    echo "- {$description}: " . ($hasPermission ? '✅ نعم' : '❌ لا') . "\n";
    
    if ($hasPermission) {
        $hasAnyPermission = true;
    }
}

echo "\nلديه صلاحيات: " . ($hasAnyPermission ? '✅ نعم' : '❌ لا') . "\n\n";

// اختبار 3: فلترة المستخدمين
echo "🧪 اختبار 3: فلترة المستخدمين\n";
$allUsers = User::count();
$filteredUsers = SuperAdminProtectionService::getFilteredUsers()->count();
echo "إجمالي المستخدمين: {$allUsers}\n";
echo "المستخدمين المفلترين: {$filteredUsers}\n";
echo "النتيجة: " . ($filteredUsers > 0 ? "✅ يرى {$filteredUsers} مستخدم" : '❌ لا يرى أي مستخدم') . "\n\n";

// اختبار 4: فلترة الأدوار
echo "🧪 اختبار 4: فلترة الأدوار\n";
$allRoles = Role::count();
$filteredRoles = SuperAdminProtectionService::getFilteredRoles()->count();
echo "إجمالي الأدوار: {$allRoles}\n";
echo "الأدوار المفلترة: {$filteredRoles}\n";
echo "النتيجة: " . ($filteredRoles > 0 ? "✅ يرى {$filteredRoles} دور" : '❌ لا يرى أي دور') . "\n\n";

// اختبار 5: الوصول لمستخدم عادي
echo "🧪 اختبار 5: الوصول لمستخدم عادي\n";
$normalUser = User::whereDoesntHave('roles', function($q) {
    $q->where('name', 'super_admin');
})->where('id', '!=', $adminUser->id)->first();

if ($normalUser) {
    $canAccess = SuperAdminProtectionService::canAccessUser($normalUser);
    $canEdit = SuperAdminProtectionService::canEditUser($normalUser);
    $canDelete = SuperAdminProtectionService::canDeleteUser($normalUser);
    
    echo "المستخدم: {$normalUser->name}\n";
    echo "الوصول: " . ($canAccess ? '✅ نعم' : '❌ لا') . "\n";
    echo "التعديل: " . ($canEdit ? '✅ نعم' : '❌ لا') . "\n";
    echo "الحذف: " . ($canDelete ? '✅ نعم' : '❌ لا') . "\n";
} else {
    echo "⚠️ لا يوجد مستخدم عادي آخر للاختبار\n";
}
echo "\n";

// اختبار 6: الوصول لدور Admin
echo "🧪 اختبار 6: الوصول لدور Admin\n";
$adminRole = Role::where('name', 'admin')->first();
if ($adminRole) {
    $canAccessRole = SuperAdminProtectionService::canAccessRole($adminRole);
    $canEditRole = SuperAdminProtectionService::canEditRole($adminRole);
    $canDeleteRole = SuperAdminProtectionService::canDeleteRole($adminRole);
    
    echo "الوصول: " . ($canAccessRole ? '✅ نعم' : '❌ لا') . "\n";
    echo "التعديل: " . ($canEditRole ? '✅ نعم' : '❌ لا') . "\n";
    echo "الحذف: " . ($canDeleteRole ? '✅ نعم' : '❌ لا') . "\n";
} else {
    echo "❌ دور admin غير موجود!\n";
}
echo "\n";

// اختبار 7: فحص دور Admin
echo "🧪 اختبار 7: فحص دور Admin\n";
$adminRole = Role::where('name', 'admin')->first();
if ($adminRole) {
    echo "✅ دور admin موجود\n";
    echo "عدد المستخدمين: " . $adminRole->users()->count() . "\n";
    echo "عدد الصلاحيات: " . $adminRole->permissions()->count() . "\n";
    
    // عرض بعض الصلاحيات
    $permissions = $adminRole->permissions()->take(10)->get();
    echo "بعض الصلاحيات:\n";
    foreach ($permissions as $permission) {
        echo "- {$permission->name}\n";
    }
} else {
    echo "❌ دور admin غير موجود!\n";
}
echo "\n";

// التوصيات
echo "📋 التوصيات:\n";
echo "=============\n";

$issues = [];

if (!$hasAnyPermission) {
    $issues[] = "المستخدم لا يملك صلاحيات Filament الأساسية";
}

if ($filteredUsers === 0) {
    $issues[] = "فلترة المستخدمين تحجب جميع المستخدمين";
}

if ($filteredRoles === 0) {
    $issues[] = "فلترة الأدوار تحجب جميع الأدوار";
}

if (!$adminRole) {
    $issues[] = "دور admin غير موجود";
} elseif ($adminRole->permissions()->count() === 0) {
    $issues[] = "دور admin لا يملك صلاحيات";
}

if (empty($issues)) {
    echo "✅ لم يتم العثور على مشاكل واضحة\n";
    echo "💡 قد تكون المشكلة في تطبيق الفلاتر في الموارد\n";
} else {
    echo "❌ المشاكل المكتشفة:\n";
    foreach ($issues as $issue) {
        echo "- {$issue}\n";
    }
}

echo "\n💡 سيتم الآن إجراء الإصلاحات المطلوبة...\n";

echo "\n";
