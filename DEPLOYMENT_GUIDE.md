# 🚀 دليل نشر مشروع مكة الذهبية

## 📋 نظرة عامة
هذا الدليل يوضح كيفية نشر مشروع مكة الذهبية على الاستضافة بنجاح مع جميع البيانات والإعدادات.

## ✅ متطلبات الاستضافة

### متطلبات الخادم:
- **PHP**: 8.1 أو أحدث
- **MySQL**: 8.0 أو أحدث  
- **Composer**: آخر إصدار
- **Node.js**: 18+ و npm
- **SSL Certificate**: مطلوب للأمان
- **Memory Limit**: 512MB على الأقل
- **Max Execution Time**: 300 ثانية على الأقل

### PHP Extensions المطلوبة:
```
- BCMath
- Ctype
- Fileinfo
- JSON
- Mbstring
- OpenSSL
- PDO
- PDO_MySQL
- Tokenizer
- XML
- GD
- Zip
- Curl
```

## 📦 خطوات النشر

### 1. تحضير الملفات
```bash
# ضغط المشروع (استثناء المجلدات غير المطلوبة)
tar -czf makkah-gold.tar.gz \
  --exclude=node_modules \
  --exclude=.git \
  --exclude=storage/logs/* \
  --exclude=storage/framework/cache/* \
  --exclude=storage/framework/sessions/* \
  --exclude=storage/framework/views/* \
  .
```

### 2. رفع الملفات
- رفع الملفات إلى مجلد الجذر للموقع
- التأكد من أن مجلد `public` هو Document Root

### 3. تكوين قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE makkah_gold_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم قاعدة البيانات
CREATE USER 'makkah_gold_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON makkah_gold_db.* TO 'makkah_gold_user'@'localhost';
FLUSH PRIVILEGES;
```

### 4. تكوين ملف .env
```env
APP_NAME="مكة الذهبية"
APP_ENV=production
APP_KEY=base64:YOUR_APP_KEY_HERE
APP_DEBUG=false
APP_URL=https://yourdomain.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=makkah_gold_db
DB_USERNAME=makkah_gold_user
DB_PASSWORD=strong_password_here

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
```

### 5. تشغيل أوامر النشر
```bash
# تثبيت Dependencies
composer install --optimize-autoloader --no-dev

# إنشاء مفتاح التطبيق
php artisan key:generate

# تشغيل Migrations
php artisan migrate

# تشغيل Seeders (إدراج البيانات الأساسية)
php artisan db:seed

# ربط مجلد التخزين
php artisan storage:link

# تحسين الأداء
php artisan config:cache
php artisan route:cache
php artisan view:cache

# بناء Assets
npm install
npm run build
```

### 6. إعدادات الصلاحيات
```bash
# إعدادات مجلدات Laravel
chmod -R 755 /path/to/your/project
chmod -R 775 storage
chmod -R 775 bootstrap/cache

# إعدادات المالك
chown -R www-data:www-data /path/to/your/project
```

## 🔐 بيانات الدخول الافتراضية

### حسابات الإدارة:
| الدور | البريد الإلكتروني | كلمة المرور |
|-------|------------------|-------------|
| Super Admin | <EMAIL> | password |
| Admin | <EMAIL> | password |
| Manager | <EMAIL> | password |

**⚠️ مهم**: يجب تغيير كلمات المرور فور النشر!

## 📊 البيانات المُدرجة تلقائياً

### الجداول الأساسية:
- ✅ **اللغات**: العربية والإنجليزية
- ✅ **الأدوار**: Super Admin, Admin, Manager, User
- ✅ **الصلاحيات**: 118 صلاحية شاملة
- ✅ **المستخدمين**: 4 مستخدمين أساسيين

### المحتوى:
- ✅ **الفئات**: 4 فئات (خواتم، أساور، قلائد، أقراط)
- ✅ **المنتجات**: 18 منتج
- ✅ **المميزات**: 4 مميزات للموقع
- ✅ **شرائح الصفحة الرئيسية**: 5 شرائح
- ✅ **الصفحات**: 7 صفحات أساسية
- ✅ **مقالات المدونة**: 3 مقالات

### المعادن والأسعار:
- ✅ **أنواع المعادن**: 3 أنواع (ذهب، فضة، بلاتين)
- ✅ **عيارات المعادن**: 15 عيار
- ✅ **أسعار المعادن**: 12 سعر نشط

### الأعمال:
- ✅ **المتاجر**: 3 متاجر
- ✅ **النشرة الإخبارية**: 5 مشتركين

## ⚙️ إعدادات الخادم

### Apache (.htaccess)
```apache
<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
```

### Nginx
```nginx
server {
    listen 80;
    listen [::]:80;
    server_name yourdomain.com;
    root /var/www/makkah-gold/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## 🔧 المهام المجدولة (Cron Jobs)

```bash
# إضافة إلى crontab
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1
```

## 🛡️ الأمان

### 1. تحديث كلمات المرور
```bash
# تغيير كلمة مرور Super Admin
php artisan tinker
>>> $user = App\Models\User::where('email', '<EMAIL>')->first();
>>> $user->password = Hash::make('new_secure_password');
>>> $user->save();
```

### 2. إعدادات الأمان
- تفعيل SSL Certificate
- إخفاء معلومات الخادم
- تحديث جميع Dependencies
- تفعيل Firewall

## 📈 مراقبة الأداء

### 1. تحسين قاعدة البيانات
```sql
-- إضافة فهارس للأداء
ALTER TABLE products ADD INDEX idx_category_active (category_id, is_active);
ALTER TABLE metal_prices ADD INDEX idx_active_date (is_active, price_date);
```

### 2. تحسين Laravel
```bash
# تحسين Autoloader
composer dump-autoload --optimize

# تحسين التكوين
php artisan optimize
```

## 🆘 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ 500 Internal Server Error
```bash
# فحص السجلات
tail -f storage/logs/laravel.log

# فحص صلاحيات المجلدات
chmod -R 775 storage bootstrap/cache
```

#### 2. مشاكل قاعدة البيانات
```bash
# فحص الاتصال
php artisan tinker
>>> DB::connection()->getPdo();
```

#### 3. مشاكل الصور
```bash
# إعادة ربط مجلد التخزين
php artisan storage:link
```

## 📞 الدعم

في حالة وجود مشاكل:
1. فحص ملفات السجلات في `storage/logs/`
2. التأكد من متطلبات الخادم
3. مراجعة إعدادات قاعدة البيانات
4. فحص صلاحيات الملفات

---

**✅ بعد اتباع هذا الدليل، سيكون موقع مكة الذهبية جاهزاً للعمل بالكامل مع جميع البيانات والإعدادات!**
