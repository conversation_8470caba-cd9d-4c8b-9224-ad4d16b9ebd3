<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\ReviewController;
use App\Http\Controllers\Api\AddressController;
use App\Http\Controllers\Api\WishlistController;
use App\Http\Controllers\Api\CartController;
use App\Http\Controllers\Api\MetalPriceController;
use App\Http\Controllers\Api\SiteStatusController;
use App\Http\Controllers\Api\ZakatCalculatorController;
use App\Models\Product;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Legacy API route for search (keeping for backward compatibility)
Route::get('/search', function (Request $request) {
    $query = $request->input('query');

    if (strlen($query) < 2) {
        return response()->json([]);
    }

    $products = Product::where('is_active', true)
        ->where(function ($q) use ($query) {
            $q->where('name_ar', 'like', '%' . $query . '%')
                ->orWhere('name_en', 'like', '%' . $query . '%')
                ->orWhere('sku', 'like', '%' . $query . '%');
        })
        ->take(10)
        ->get(['id', 'name_ar', 'name_en', 'slug', 'price', 'image']);

    return response()->json($products);
});

// Public routes
Route::prefix('v1')->group(function () {
    // Authentication
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    Route::post('forgot-password', [AuthController::class, 'forgotPassword']);

    // Products
    Route::get('products', [ProductController::class, 'index']);
    Route::get('products/{id}', [ProductController::class, 'show']);

    // Categories
    Route::get('categories', [CategoryController::class, 'index']);
    Route::get('categories/{id}', [CategoryController::class, 'show']);

    // Reviews
    Route::get('reviews', [ReviewController::class, 'index']);
    Route::get('reviews/{id}', [ReviewController::class, 'show']);

    // Metal Prices
    Route::get('metal-prices', [MetalPriceController::class, 'index']);
    Route::get('metal-prices/latest', [MetalPriceController::class, 'latest']);
    Route::get('metal-prices/history', [MetalPriceController::class, 'history']);
    Route::get('metal-prices/{id}', [MetalPriceController::class, 'show']);

    // Search
    Route::get('search', [ProductController::class, 'index']);

    // Site Status
    Route::get('check-display-mode', [SiteStatusController::class, 'checkDisplayMode']);
    Route::get('check-features', [SiteStatusController::class, 'checkFeatures']);
    Route::get('check-payment-methods', [SiteStatusController::class, 'checkPaymentMethods']);
});

// Mobile App API Routes
Route::prefix('app/v1')->group(function () {
    // أسعار المعادن للتطبيق
    Route::get('metal-prices/current', [MetalPriceController::class, 'getCurrentPrices']);
    Route::get('metal-prices/{metalType}', [MetalPriceController::class, 'getMetalPrices']);
    Route::get('metal-prices/{metalType}/{purity}/history', [MetalPriceController::class, 'getPriceHistory']);
    Route::post('metal-prices/calculate-jewelry-value', [MetalPriceController::class, 'calculateJewelryValue']);

    // حاسبة الزكاة
    Route::post('zakat/gold', [ZakatCalculatorController::class, 'calculateGoldZakat']);
    Route::post('zakat/silver', [ZakatCalculatorController::class, 'calculateSilverZakat']);
    Route::get('zakat/nisab-info', [ZakatCalculatorController::class, 'getNisabInfo']);

    // الفئات والمنتجات للتطبيق
    Route::get('categories', [CategoryController::class, 'getForApp']);
    Route::get('categories/{id}/products', [CategoryController::class, 'getCategoryWithProducts']);

    Route::get('products', [ProductController::class, 'getForApp']);
    Route::get('products/{id}', [ProductController::class, 'getProduct']);
    Route::get('products/featured', [ProductController::class, 'getFeaturedProducts']);
});

// Protected routes
Route::prefix('v1')->middleware(['auth:sanctum'])->group(function () {
    // User
    Route::get('user', [AuthController::class, 'user']);
    Route::post('logout', [AuthController::class, 'logout']);
    Route::put('user/profile', [AuthController::class, 'updateProfile']);

    // Addresses
    Route::get('addresses', [AddressController::class, 'index']);
    Route::post('addresses', [AddressController::class, 'store']);
    Route::get('addresses/{id}', [AddressController::class, 'show']);
    Route::put('addresses/{id}', [AddressController::class, 'update']);
    Route::delete('addresses/{id}', [AddressController::class, 'destroy']);

    // Wishlist
    Route::get('wishlist', [WishlistController::class, 'index']);
    Route::post('wishlist', [WishlistController::class, 'store']);
    Route::delete('wishlist/{id}', [WishlistController::class, 'destroy']);
    Route::get('wishlist/check/{id}', [WishlistController::class, 'check']);
    Route::delete('wishlist', [WishlistController::class, 'clear']);

    // Cart
    Route::get('cart', [CartController::class, 'index']);
    Route::post('cart/items', [CartController::class, 'addItem']);
    Route::put('cart/items/{id}', [CartController::class, 'updateItem']);
    Route::delete('cart/items/{id}', [CartController::class, 'removeItem']);
    Route::delete('cart', [CartController::class, 'clear']);

    // Orders
    Route::get('orders', [OrderController::class, 'index']);
    Route::post('orders', [OrderController::class, 'store']);
    Route::get('orders/{id}', [OrderController::class, 'show']);
    Route::post('orders/{id}/cancel', [OrderController::class, 'cancel']);

    // Reviews
    Route::post('reviews', [ReviewController::class, 'store']);
    Route::put('reviews/{id}', [ReviewController::class, 'update']);
    Route::delete('reviews/{id}', [ReviewController::class, 'destroy']);

    // Products (Admin only)
    Route::middleware('admin')->group(function () {
        Route::post('products', [ProductController::class, 'store']);
        Route::put('products/{id}', [ProductController::class, 'update']);
        Route::delete('products/{id}', [ProductController::class, 'destroy']);

        // Categories (Admin only)
        Route::post('categories', [CategoryController::class, 'store']);
        Route::put('categories/{id}', [CategoryController::class, 'update']);
        Route::delete('categories/{id}', [CategoryController::class, 'destroy']);

        // Metal Prices (Admin only)
        Route::post('metal-prices', [MetalPriceController::class, 'store']);
        Route::put('metal-prices/{id}', [MetalPriceController::class, 'update']);
        Route::delete('metal-prices/{id}', [MetalPriceController::class, 'destroy']);
    });
});
