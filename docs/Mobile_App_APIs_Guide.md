# دليل APIs تطبيق مكة الذهبية للموبايل

## نظرة عامة

تم تطوير مجموعة APIs مخصصة لتطبيق مكة الذهبية للموبايل تتضمن الوظائف الأساسية التالية:

- **أسعار المعادن المباشرة**: عرض أسعار الذهب والفضة بجميع العيارات
- **حاسبة الزكاة**: حساب زكاة الذهب والفضة
- **حاسبة قيمة المجوهرات**: تقدير قيمة المجوهرات
- **عرض الفئات والمنتجات**: استعراض المنتجات (بدون شراء)

## Base URL

```
http://makkah-gold-jewelry.test/api/app/v1
```

## الاستجابة المعيارية

جميع APIs تُرجع استجابة بالتنسيق التالي:

```json
{
    "success": true|false,
    "message": "رسالة باللغة العربية",
    "data": {}, // البيانات المطلوبة
    "error": "رسالة الخطأ (في حالة الفشل)"
}
```

## 1. أسعار المعادن

### 1.1 الحصول على أحدث أسعار المعادن

```http
GET /metal-prices/current
```

**الاستجابة:**
```json
{
    "success": true,
    "message": "تم جلب أسعار المعادن بنجاح",
    "data": [
        {
            "metal_type": "gold",
            "metal_name": "الذهب",
            "prices": [
                {
                    "id": 1,
                    "purity": "24K",
                    "purity_name": "24 قيراط",
                    "price_per_gram": 5280,
                    "price_per_ounce": 164226.48,
                    "price_per_piece": 0,
                    "currency": "EGP",
                    "last_updated": "2025-01-29 06:30:00",
                    "last_updated_human": "منذ 5 دقائق"
                }
            ]
        }
    ]
}
```

### 1.2 أسعار معدن محدد

```http
GET /metal-prices/{metalType}
```

**المعاملات:**
- `metalType`: نوع المعدن (gold, silver, platinum)

### 1.3 تاريخ الأسعار

```http
GET /metal-prices/{metalType}/{purity}/history?days=30
```

**المعاملات:**
- `metalType`: نوع المعدن
- `purity`: العيار (24K, 21K, 18K, etc.)
- `days`: عدد الأيام (افتراضي: 30)

### 1.4 حساب قيمة المجوهرات

```http
POST /metal-prices/calculate-jewelry-value
```

**البيانات المطلوبة:**
```json
{
    "metal_type": "gold",
    "purity": "21K",
    "weight": 10.5,
    "unit": "gram"
}
```

## 2. حاسبة الزكاة

### 2.1 معلومات النصاب

```http
GET /zakat/nisab-info
```

**الاستجابة:**
```json
{
    "success": true,
    "data": {
        "gold_nisab": {
            "weight_grams": 85,
            "current_value": 448800,
            "price_per_gram": 5280
        },
        "silver_nisab": {
            "weight_grams": 595,
            "current_value": 35105,
            "price_per_gram": 59
        },
        "zakat_rate": "2.5%"
    }
}
```

### 2.2 حساب زكاة الذهب

```http
POST /zakat/gold
```

**البيانات المطلوبة:**
```json
{
    "items": [
        {
            "name": "خاتم ذهب",
            "weight": 10,
            "purity": "21K"
        },
        {
            "name": "سلسلة ذهب",
            "weight": 25,
            "purity": "18K"
        }
    ]
}
```

### 2.3 حساب زكاة الفضة

```http
POST /zakat/silver
```

**البيانات المطلوبة:**
```json
{
    "items": [
        {
            "name": "خاتم فضة",
            "weight": 15,
            "purity": "925"
        }
    ]
}
```

## 3. الفئات

### 3.1 جميع الفئات

```http
GET /categories
```

### 3.2 فئة محددة مع منتجاتها

```http
GET /categories/{id}/products
```

## 4. المنتجات

### 4.1 جميع المنتجات

```http
GET /products?per_page=20&sort_by=created_at&sort_direction=desc
```

**المعاملات الاختيارية:**
- `per_page`: عدد المنتجات في الصفحة (افتراضي: 20)
- `sort_by`: ترتيب حسب (created_at, price, name, weight)
- `sort_direction`: اتجاه الترتيب (asc, desc)
- `category_id`: فلترة حسب الفئة
- `featured`: المنتجات المميزة فقط
- `material_type`: نوع المعدن
- `search`: البحث في الاسم والوصف

### 4.2 منتج محدد

```http
GET /products/{id}
```

### 4.3 المنتجات المميزة

```http
GET /products/featured
```

## معالجة الأخطاء

في حالة حدوث خطأ، ستحصل على استجابة مثل:

```json
{
    "success": false,
    "message": "رسالة الخطأ باللغة العربية",
    "error": "تفاصيل الخطأ التقنية"
}
```

## أكواد الحالة HTTP

- `200`: نجح الطلب
- `404`: المورد غير موجود
- `422`: خطأ في التحقق من البيانات
- `500`: خطأ في الخادم

## ملاحظات مهمة

1. جميع الأسعار بالجنيه المصري (EGP)
2. جميع الأوزان بالجرام
3. جميع الرسائل باللغة العربية
4. التواريخ بتنسيق ISO 8601
5. الصور تُرجع كـ URLs كاملة

## اختبار APIs

استخدم Postman Collection المرفقة لاختبار جميع APIs:
- `Makkah_Gold_Mobile_App_APIs.postman_collection.json`
- `Makkah_Gold_Environment.postman_environment.json`

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
