# خطة تطوير تطبيق مكة الذهبية للموبايل

## المرحلة الأولى: APIs ✅ مكتملة

### ما تم إنجازه:

#### 1. APIs أسعار المعادن
- ✅ `MetalPriceController` محدث بـ methods جديدة للتطبيق
- ✅ `getCurrentPrices()` - جلب أحدث أسعار جميع المعادن
- ✅ `getMetalPrices($metalType)` - أسعار معدن محدد
- ✅ `getPriceHistory($metalType, $purity)` - تاريخ الأسعار مع إحصائيات
- ✅ `calculateJewelryValue()` - حساب قيمة المجوهرات

#### 2. APIs حاسبة الزكاة
- ✅ `ZakatCalculatorController` جديد
- ✅ `calculateGoldZakat()` - حساب زكاة الذهب لعدة قطع
- ✅ `calculateSilverZakat()` - حساب زكاة الفضة لعدة قطع
- ✅ `getNisabInfo()` - معلومات النصاب مع الأسعار الحالية

#### 3. APIs الفئات والمنتجات
- ✅ `CategoryController` محدث بـ methods للتطبيق
- ✅ `getForApp()` - جلب الفئات مع الفئات الفرعية
- ✅ `getCategoryWithProducts($id)` - فئة مع منتجاتها
- ✅ `ProductController` محدث بـ methods للتطبيق
- ✅ `getForApp()` - جلب المنتجات مع فلترة وترتيب
- ✅ `getProduct($id)` - منتج محدد
- ✅ `getFeaturedProducts()` - المنتجات المميزة

#### 4. Routes والتوثيق
- ✅ Routes جديدة تحت `/api/app/v1`
- ✅ Postman Collection كاملة
- ✅ Environment file للـ Postman
- ✅ دليل APIs مفصل

### الميزات المتاحة:

#### أسعار المعادن:
- عرض أسعار الذهب (24K, 22K, 21K, 18K, 14K, 12K, 9K)
- عرض أسعار الفضة (999, 925, 900, 800, 600)
- تاريخ الأسعار مع رسوم بيانية
- حساب قيمة المجوهرات بالوزن والعيار

#### حاسبة الزكاة:
- حساب زكاة الذهب لعدة قطع مختلفة العيارات
- حساب زكاة الفضة لعدة قطع مختلفة العيارات
- عرض معلومات النصاب مع الأسعار الحالية
- حساب الوزن الخالص للمعادن

#### الفئات والمنتجات:
- عرض الفئات مع الفئات الفرعية
- عرض المنتجات مع إمكانية الفلترة والبحث
- تفاصيل المنتجات مع الصور والمعلومات
- المنتجات المميزة

## المرحلة الثانية: تطوير التطبيق (القادمة)

### التقنيات المطلوبة:
- **Expo React Native** (أحدث إصدار)
- **TypeScript** للكتابة الآمنة
- **React Navigation** للتنقل
- **Axios** لـ API calls
- **AsyncStorage** للتخزين المحلي
- **React Native Paper** أو **NativeBase** للـ UI Components

### الشاشات المطلوبة:

#### 1. الشاشة الرئيسية
- أسعار المعادن الحالية
- المنتجات المميزة
- روابط سريعة للحاسبات

#### 2. شاشة أسعار المعادن
- قائمة بجميع أسعار المعادن
- تحديث تلقائي للأسعار
- رسوم بيانية لتاريخ الأسعار
- مؤشرات التغيير (صعود/هبوط)

#### 3. شاشة حاسبة الزكاة
- تبويبات للذهب والفضة
- إضافة/حذف قطع المجوهرات
- حساب تلقائي للزكاة
- عرض معلومات النصاب

#### 4. شاشة حاسبة قيمة المجوهرات
- اختيار نوع المعدن والعيار
- إدخال الوزن
- حساب القيمة الحالية

#### 5. شاشة الفئات
- عرض الفئات في شبكة
- عداد المنتجات لكل فئة
- البحث في الفئات

#### 6. شاشة المنتجات
- قائمة المنتجات مع الصور
- فلترة حسب الفئة والسعر
- البحث في المنتجات
- ترتيب النتائج

#### 7. شاشة تفاصيل المنتج
- صور المنتج (معرض)
- تفاصيل المنتج (الوزن، العيار، السعر)
- منتجات مشابهة

#### 8. شاشة الإعدادات
- تغيير اللغة
- الوضع الليلي/النهاري
- إعدادات الإشعارات
- معلومات التطبيق

### الميزات الإضافية:

#### إشعارات الأسعار:
- تنبيهات عند تغيير أسعار المعادن
- إعداد حدود للتنبيهات
- إشعارات يومية للأسعار

#### المفضلة:
- حفظ المنتجات المفضلة
- قائمة المفضلة
- مشاركة المنتجات

#### معلومات المتاجر:
- مواقع المتاجر على الخريطة
- معلومات الاتصال
- ساعات العمل

#### المدونة:
- عرض مقالات المدونة
- البحث في المقالات
- مشاركة المقالات

### التصميم والواجهة:

#### الألوان:
- ذهبي أساسي: `#FFD700`
- ذهبي داكن: `#B8860B`
- أبيض: `#FFFFFF`
- رمادي فاتح: `#F5F5F5`
- أسود: `#000000`

#### الخطوط:
- خط عربي: `Cairo` أو `Tajawal`
- خط إنجليزي: `Roboto`

#### الأيقونات:
- استخدام `react-native-vector-icons`
- أيقونات من `MaterialIcons` و `FontAwesome`

### خطة التطوير:

#### الأسبوع الأول:
- إعداد مشروع Expo
- تصميم الشاشات الأساسية
- إعداد Navigation
- ربط APIs أسعار المعادن

#### الأسبوع الثاني:
- تطوير شاشات حاسبة الزكاة
- تطوير شاشة حاسبة قيمة المجوهرات
- إضافة الرسوم البيانية

#### الأسبوع الثالث:
- تطوير شاشات الفئات والمنتجات
- إضافة البحث والفلترة
- تطوير شاشة تفاصيل المنتج

#### الأسبوع الرابع:
- إضافة الميزات الإضافية
- تحسين التصميم والأداء
- اختبار شامل للتطبيق
- إعداد للنشر

### متطلبات النشر:

#### Android:
- إنشاء حساب Google Play Developer
- إعداد App Bundle
- اختبار على أجهزة مختلفة

#### iOS:
- إنشاء حساب Apple Developer
- إعداد App Store Connect
- اختبار على أجهزة iOS

### الاختبار:

#### اختبار الوحدة:
- اختبار APIs
- اختبار الحسابات
- اختبار التنقل

#### اختبار التكامل:
- اختبار ربط APIs
- اختبار تدفق البيانات
- اختبار الأداء

#### اختبار المستخدم:
- اختبار سهولة الاستخدام
- اختبار على مستخدمين حقيقيين
- جمع التعليقات والتحسين

## الملفات المتاحة:

1. **APIs Controllers:**
   - `app/Http/Controllers/Api/MetalPriceController.php`
   - `app/Http/Controllers/Api/ZakatCalculatorController.php`
   - `app/Http/Controllers/Api/CategoryController.php`
   - `app/Http/Controllers/Api/ProductController.php`

2. **Routes:**
   - `routes/api.php` (محدث بـ routes التطبيق)

3. **Postman:**
   - `postman/Makkah_Gold_Mobile_App_APIs.postman_collection.json`
   - `postman/Makkah_Gold_Environment.postman_environment.json`

4. **التوثيق:**
   - `docs/Mobile_App_APIs_Guide.md`
   - `docs/Mobile_App_Development_Plan.md`

## الخطوات التالية:

1. **مراجعة وتجربة APIs** باستخدام Postman
2. **إعداد مشروع Expo React Native**
3. **تصميم واجهات المستخدم**
4. **تطوير الشاشات تدريجياً**
5. **اختبار وتحسين التطبيق**
6. **النشر على المتاجر**

## ملاحظات مهمة:

- جميع APIs تدعم اللغة العربية
- الأسعار بالجنيه المصري
- التطبيق يدعم RTL للعربية
- تصميم متجاوب لجميع أحجام الشاشات
- معالجة شاملة للأخطاء
