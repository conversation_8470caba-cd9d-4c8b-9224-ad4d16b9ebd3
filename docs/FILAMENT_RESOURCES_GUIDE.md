# دليل إضافة الموارد الجديدة في Filament

## نظرة عامة
هذا الدليل يوضح كيفية إضافة موارد جديدة في Filament مع نظام الصلاحيات المطبق في المشروع.

## إضافة مورد جديد - الخطوات البسيطة

### الخطوة 1: إنشاء المورد
```bash
# إنشاء مورد جديد
php artisan make:filament-resource CustomerResource

# أو مع نموذج جديد
php artisan make:filament-resource CustomerResource --generate
```

### الخطوة 2: إضافة trait الصلاحيات
افتح الملف المُنشأ `app/Filament/Resources/CustomerResource.php` وأضف:

```php
<?php

namespace App\Filament\Resources;

use App\Traits\HasPermissionFiltering;  // ← إضافة هذا
use Filament\Resources\Resource;

class CustomerResource extends Resource
{
    use HasPermissionFiltering;  // ← إضافة هذا
    
    protected static ?string $model = Customer::class;
    
    // باقي الكود العادي...
}
```

### الخطوة 3: إنشاء الصلاحيات
```bash
# إنشاء الصلاحيات تلقائياً لجميع الموارد
php artisan shield:generate

# أو لمورد محدد فقط
php artisan shield:generate --resource=CustomerResource
```

### الخطوة 4: تحديث قائمة الموارد (اختياري)
في `app/Services/FilamentPermissionService.php`:

```php
public static function getAvailableResourceClasses(): array
{
    $allResources = [
        // الموارد الموجودة...
        \App\Filament\Resources\CustomerResource::class,  // ← إضافة هذا
    ];
    
    return static::filterResources($allResources);
}
```

## ✅ هذا كل شيء!

### النتائج التلقائية:
- ✅ **الصلاحيات المُنشأة**: `view_any_customer`, `create_customer`, `update_customer`, `delete_customer`
- ✅ **الفلترة التلقائية**: المورد يظهر فقط للمستخدمين الذين لديهم صلاحية
- ✅ **القائمة الجانبية**: تُحدث تلقائياً
- ✅ **أزرار الإجراءات**: تُفلتر حسب الصلاحيات

## إضافة صفحة جديدة

### الخطوة 1: إنشاء الصفحة
```bash
php artisan make:filament-page ReportsPage
```

### الخطوة 2: إضافة trait الصلاحيات
```php
<?php

namespace App\Filament\Pages;

use App\Traits\HasPagePermissionFiltering;  // ← إضافة هذا
use Filament\Pages\Page;

class ReportsPage extends Page
{
    use HasPagePermissionFiltering;  // ← إضافة هذا
    
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    
    // باقي الكود...
}
```

### الخطوة 3: إنشاء الصلاحية يدوياً
```php
// في seeder أو console
Permission::create(['name' => 'page_ReportsPage']);
```

## إضافة ودجة جديدة

### الخطوة 1: إنشاء الودجة
```bash
php artisan make:filament-widget CustomStatsWidget --stats-overview
```

### الخطوة 2: إضافة trait الصلاحيات
```php
<?php

namespace App\Filament\Widgets;

use App\Traits\HasWidgetPermissionFiltering;  // ← إضافة هذا
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class CustomStatsWidget extends BaseWidget
{
    use HasWidgetPermissionFiltering;  // ← إضافة هذا
    
    // باقي الكود...
}
```

### الخطوة 3: إنشاء الصلاحية يدوياً
```php
// في seeder أو console
Permission::create(['name' => 'widget_CustomStatsWidget']);
```

## تعيين الصلاحيات للأدوار

### عبر واجهة الإدارة:
1. اذهب إلى **إدارة الأدوار**
2. اختر الدور المطلوب (مثل `admin`)
3. حدد الصلاحيات الجديدة
4. احفظ التغييرات

### عبر Seeder:
```php
// في database/seeders/RolePermissionSeeder.php
$adminRole = Role::findByName('admin');
$adminRole->givePermissionTo([
    'view_any_customer',
    'create_customer',
    'update_customer',
    'delete_customer',
]);
```

## أنماط تسمية الصلاحيات

### للموارد:
- `view_any_{resource}` - عرض قائمة السجلات
- `create_{resource}` - إنشاء سجل جديد
- `update_{resource}` - تعديل سجل موجود
- `delete_{resource}` - حذف سجل

### للصفحات:
- `page_{PageName}` - الوصول للصفحة

### للودجات:
- `widget_{WidgetName}` - عرض الودجة

### الحالات الخاصة:
- `view_any_blog::post` - للموارد المركبة
- `view_any_super::admin::setting` - للإعدادات المتقدمة

## نصائح مهمة

### 1. ترتيب الأولوية:
```php
// في المورد
protected static ?int $navigationSort = 10;
```

### 2. تجميع الموارد:
```php
// في المورد
protected static ?string $navigationGroup = 'إدارة العملاء';
```

### 3. إخفاء من التنقل:
```php
// في المورد
protected static bool $shouldRegisterNavigation = false;
```

### 4. تخصيص الأيقونة:
```php
// في المورد
protected static ?string $navigationIcon = 'heroicon-o-users';
```

## استكشاف الأخطاء

### المورد لا يظهر في القائمة:
1. تأكد من إضافة `HasPermissionFiltering` trait
2. تأكد من وجود صلاحية `view_any_{resource}`
3. تأكد من تعيين الصلاحية للمستخدم/الدور

### الأزرار لا تظهر:
1. تأكد من وجود الصلاحيات المطلوبة (`create_`, `update_`, `delete_`)
2. تأكد من تعيين الصلاحيات للمستخدم

### الودجة لا تظهر:
1. تأكد من إضافة `HasWidgetPermissionFiltering` trait
2. تأكد من وجود صلاحية `widget_{WidgetName}`
3. تأكد من إضافة الودجة لقائمة الودجات في الصفحة

## أوامر مفيدة

```bash
# عرض جميع الصلاحيات
php artisan permission:show

# إنشاء صلاحيات لجميع الموارد
php artisan shield:generate

# مسح cache الصلاحيات
php artisan permission:cache-reset

# عرض الأدوار والصلاحيات
php artisan shield:super-admin

# اختبار نظام الصلاحيات
php test_filament_permissions.php
```

## الخلاصة

مع النظام المطبق، إضافة مورد جديد يحتاج:
- ⏱️ **2-3 دقائق** فقط
- 🔧 **خطوتان بسيطتان** (إضافة trait + إنشاء صلاحيات)
- ✅ **نظام صلاحيات كامل** يعمل تلقائياً

النظام يوفر **مرونة عالية** مع **سهولة في الاستخدام** ويضمن **الأمان** في جميع مستويات التطبيق.
