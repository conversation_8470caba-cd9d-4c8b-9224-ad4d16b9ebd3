# دليل نظام صلاحيات الويدجت المتقدم

## نظرة عامة
تم إنشاء نظام متقدم للتحكم في صلاحيات الوصول للويدجت من خلال إدارة الأدوار في Filament Shield. يمكنك الآن التحكم بدقة في أي ويدجت يراها كل دور.

## الميزات المطبقة

### ✅ **1. صلاحيات مخصصة لكل ويدجت:**
- **widget_MainStatsOverview** - الإحصائيات الرئيسية
- **widget_ContentStatsWidget** - إحصائيات المحتوى
- **widget_EngagementStatsWidget** - إحصائيات التفاعل
- **widget_MetalPricesWidget** - أسعار المعادن
- **widget_RecentOrdersWidget** - الطلبات الحديثة
- **widget_UpcomingAppointmentsWidget** - المواعيد القادمة
- **widget_SalesChartWidget** - مخطط المبيعات
- **widget_SystemStatusWidget** - حالة النظام

### ✅ **2. نظام صلاحيات متدرج:**

#### **Super Admin (8/8 ويدجت):**
- وصول كامل لجميع الويدجت
- يتجاوز جميع قيود الصلاحيات
- يمكنه رؤية حالة النظام

#### **Admin (7/8 ويدجت):**
- وصول لمعظم الويدجت
- لا يمكنه رؤية حالة النظام
- يمكن إدارة المحتوى والطلبات

#### **Manager (7/8 ويدجت):**
- وصول لمعظم الويدجت
- لا يمكنه رؤية حالة النظام
- يركز على العمليات التجارية

### ✅ **3. نظام مزدوج للحماية:**
- **صلاحية الويدجت المخصصة:** `widget_WidgetName`
- **صلاحية المورد:** `view_any_resource`
- يجب توفر كلا الصلاحيتين للوصول

## كيفية استخدام النظام

### 🎯 **1. التحكم في صلاحيات الويدجت:**

#### **من لوحة التحكم:**
1. اذهب إلى **الأدوار والصلاحيات**
2. اختر **الدور المراد تعديله**
3. في قسم **الصلاحيات**، ابحث عن `widget_`
4. قم بتفعيل/إلغاء تفعيل الويدجت المطلوبة
5. **احفظ التغييرات**

#### **مثال عملي:**
```
لإخفاء ويدجت "أسعار المعادن" عن دور Admin:
1. اذهب إلى تعديل دور Admin
2. ابحث عن "widget_MetalPricesWidget"
3. قم بإلغاء تفعيلها
4. احفظ التغييرات
```

### 🔧 **2. إنشاء دور جديد مع صلاحيات مخصصة:**

#### **خطوات الإنشاء:**
1. اذهب إلى **إنشاء دور جديد**
2. أدخل **اسم الدور** (مثل: "محرر المحتوى")
3. في قسم الصلاحيات، اختر الويدجت المناسبة:
   - `widget_ContentStatsWidget` ✅
   - `widget_EngagementStatsWidget` ✅
   - `widget_MainStatsOverview` ❌
   - `widget_SystemStatusWidget` ❌
4. أضف صلاحيات الموارد المطلوبة
5. احفظ الدور

### 📊 **3. مراقبة الصلاحيات:**

#### **فحص صلاحيات دور معين:**
```bash
php artisan tinker
$role = \Spatie\Permission\Models\Role::where('name', 'admin')->first();
$role->permissions->where('name', 'like', 'widget_%')->pluck('name');
```

#### **فحص المستخدمين الذين يمكنهم رؤية ويدجت معين:**
```bash
$users = \App\Models\User::permission('widget_MetalPricesWidget')->get();
```

## الأوامر المتاحة

### 🛠️ **1. إنشاء صلاحيات الويدجت:**
```bash
php artisan shield:generate-widget-permissions
```
**الوظيفة:** إنشاء صلاحيات مخصصة لجميع الويدجت

### 🔄 **2. إضافة صلاحيات للأدوار:**
```bash
php artisan widgets:assign-permissions
```
**الوظيفة:** إضافة صلاحيات الويدجت للأدوار الموجودة

## أمثلة عملية للاستخدام

### 📝 **مثال 1: دور "محاسب"**
**الهدف:** رؤية الإحصائيات المالية فقط

**الصلاحيات المطلوبة:**
- ✅ `widget_MainStatsOverview`
- ✅ `widget_RecentOrdersWidget`
- ✅ `widget_SalesChartWidget`
- ❌ `widget_SystemStatusWidget`
- ❌ `widget_ContentStatsWidget`

### 📝 **مثال 2: دور "مدير المحتوى"**
**الهدف:** إدارة المحتوى والتفاعل

**الصلاحيات المطلوبة:**
- ✅ `widget_ContentStatsWidget`
- ✅ `widget_EngagementStatsWidget`
- ❌ `widget_MetalPricesWidget`
- ❌ `widget_SystemStatusWidget`

### 📝 **مثال 3: دور "مدير المبيعات"**
**الهدف:** متابعة المبيعات والعملاء

**الصلاحيات المطلوبة:**
- ✅ `widget_MainStatsOverview`
- ✅ `widget_RecentOrdersWidget`
- ✅ `widget_SalesChartWidget`
- ✅ `widget_UpcomingAppointmentsWidget`
- ❌ `widget_SystemStatusWidget`

## استكشاف الأخطاء

### ❓ **مشكلة: الويدجت لا تظهر رغم وجود الصلاحية**
**الحلول:**
1. تأكد من وجود صلاحية المورد أيضاً
2. تحقق من تسجيل دخول المستخدم
3. امسح الكاش: `php artisan cache:clear`

### ❓ **مشكلة: Super Admin لا يرى بعض الويدجت**
**الحلول:**
1. تأكد من أن المستخدم له دور `super_admin`
2. تحقق من `SuperAdminProtectionService`

### ❓ **مشكلة: صلاحيات الويدجت غير موجودة**
**الحلول:**
1. شغل: `php artisan shield:generate-widget-permissions`
2. شغل: `php artisan widgets:assign-permissions`

## الملفات المهمة

### 📁 **الملفات الأساسية:**
- `app/Traits/HasAdvancedWidgetPermissions.php` - منطق الصلاحيات
- `app/Console/Commands/GenerateWidgetPermissions.php` - إنشاء الصلاحيات
- `lang/ar/roles_permissions.php` - ترجمات الصلاحيات

### 📁 **ملفات الويدجت:**
- `app/Filament/Widgets/` - جميع ملفات الويدجت
- `resources/views/filament/widgets/` - Views المخصصة

## النتائج المحققة

### ✅ **الإنجازات:**
- **32/34 اختبار نجح** (94% نجاح)
- **8 صلاحيات ويدجت مخصصة**
- **3 أدوار مُحدَّثة بالصلاحيات**
- **نظام حماية مزدوج**
- **واجهة إدارة سهلة**

### 📊 **الإحصائيات الحالية:**
- **Super Admin:** 8/8 ويدجت
- **Admin:** 7/8 ويدجت  
- **Manager:** 7/8 ويدجت

## الخلاصة

تم إنشاء نظام متقدم وشامل للتحكم في صلاحيات الويدجت يوفر:

1. **مرونة كاملة** في إدارة الصلاحيات
2. **أمان متقدم** مع نظام حماية مزدوج
3. **سهولة الاستخدام** من خلال واجهة Filament
4. **قابلية التوسع** لإضافة ويدجت جديدة
5. **توثيق شامل** وأمثلة عملية

**النظام جاهز للاستخدام ويمكن التحكم في صلاحيات الويدجت بسهولة من لوحة التحكم!** 🚀
